import React, { useEffect, useState, useRef, useMemo } from 'react';
import { format } from 'date-fns';
import { ChevronUp, ChevronDown, Battery, Wifi, WifiOff, Camera, Phone, Monitor, X } from 'lucide-react';
import { LiveViewResponse, VisionMaxAPI } from '../../api/visionMaxApi';
import { v4 as uuidv4 } from 'uuid';
import VisionMaxLiveView from './VisionMaxLiveView'; // Updated import
import ConnectionProgressModal from './ConnectionProgressModal';
import DisclaimerModal from './DisclaimerModal';
import { log } from 'console';

interface Device {
    id: string;
    model: string;
    status: string;
    serialNumber: string;
    planType: string;
    activatedSince: string;
    lastSeen?: string;
    appVersion: string;
    adasHealth: string;
    batteryLevel?: number;
    signalStrength?: number;
    assignedTo?: {
        driverId: string;
        driverName: string;
    };
    cid: string;
}

interface DevicesTableProps {
    devices: Device[];
    isLoading: boolean;
    onRowClick?: (deviceId: string) => void;
}

type SortField = keyof Device | 'assignedTo' | '';
type SortDirection = 'asc' | 'desc';

// Define the structure expected by VisionMaxLiveView's credentials prop
interface LiveViewCredentials {
    key: string;
    secret: string;
    token: string;
    topics: {
        credential: string;
        device_report: string;
    };
    client_id: string[];
}

// Define the structure of the data expected in liveViewData
interface LiveViewData {
    credentials: LiveViewCredentials;
    request_id: string;
    remaining_time: number;
    region: string;
    deviceGateway: string;
}

export const DevicesTable: React.FC<DevicesTableProps> = ({
    devices,
    isLoading,
    onRowClick,
}) => {
    const [sortField, setSortField] = useState<SortField>('');
    const [sortDirection, setSortDirection] = useState<SortDirection>('asc');

    // --- State for Live View Control ---
    const [currentLiveView, setCurrentLiveView] = useState<{
        device: Device | null;
        connectionProgress: number;
        liveViewData: LiveViewData | null;
    }>({
        device: null,
        connectionProgress: 0,
        liveViewData: null,
    });
    const [isStartingLiveView, setIsStartingLiveView] = useState(false);
    const currentRequestIdRef = useRef<string | null>(null);

    // --- State for Disclaimer ---
    const [showLiveViewDisclaimer, setShowLiveViewDisclaimer] = useState(true);
    const [dontShowAgain, setDontShowAgain] = useState(false);

    // --- API Instance ---
    const token = typeof window !== 'undefined' ? localStorage.getItem('authToken') : null;
    const api = useMemo(() => new VisionMaxAPI({
        baseURL: 'https://api.visionmaxfleet.com',
        token: token ?? undefined
    }), [token]);

    // --- Sorting Logic ---
    const handleSort = (field: SortField) => {
        if (field === sortField) {
            setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
        } else {
            setSortField(field);
            setSortDirection('asc');
        }
    };

    const sortedDevices = useMemo(() => {
        if (!sortField) return devices;
        return [...devices].sort((a, b) => {
            let aValue: any = sortField === 'assignedTo' ? a.assignedTo?.driverName : a[sortField as keyof Omit<Device, 'assignedTo'>];
            let bValue: any = sortField === 'assignedTo' ? b.assignedTo?.driverName : b[sortField as keyof Omit<Device, 'assignedTo'>];

            if (sortField === 'activatedSince' || sortField === 'lastSeen') {
                aValue = aValue ? new Date(aValue).getTime() : 0;
                bValue = bValue ? new Date(bValue).getTime() : 0;
            }

            if (aValue === undefined || aValue === null) aValue = '';
            if (bValue === undefined || bValue === null) bValue = '';

            const comparison = String(aValue).localeCompare(String(bValue));
            return sortDirection === 'asc' ? comparison : -comparison;
        });
    }, [devices, sortField, sortDirection]);

    // --- UI Helper Functions ---
    const formatDate = (dateString?: string) => {
        if (!dateString) return 'N/A';
        try {
            return format(new Date(dateString), 'MMM d, yyyy, HH:mm');
        } catch (error) {
            console.error("Error formatting date:", error);
            return dateString;
        }
    };

    const getStatusIndicator = (status: Device['status']) => {
        const statusLower = status?.toLowerCase() || 'unknown';
        
        switch (statusLower) {
            case 'online': 
                return (
                    <div className="flex items-center pointer-events-none">
                        <span className="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse"></span>
                        <span className="text-green-600">Online</span>
                    </div>
                );
            case 'offline': 
                return (
                    <div className="flex items-center">
                        <span className="w-2 h-2 bg-gray-500 rounded-full mr-2"></span>
                        <span className="text-gray-600">Offline</span>
                    </div>
                );
            default: 
                return (
                    <div className="flex items-center">
                        <span className="w-2 h-2 bg-yellow-500 rounded-full mr-2"></span>
                        <span className="text-yellow-600">{status || 'Unknown'}</span>
                    </div>
                );
        }
    };

    const getHealthBadge = (health?: Device['adasHealth']) => {
        switch (health) {
            case 'Healthy': return <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Healthy</span>;
            case 'Warning': return <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">Warning</span>;
            case 'Error': return <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">Error</span>;
            default: return <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">{health || 'N/A'}</span>;
        }
    };

    const getBatteryIndicator = (level?: number) => {
        if (level === undefined || level === null) return null;
        const color = level > 70 ? 'text-green-500' : level > 30 ? 'text-yellow-500' : 'text-red-500';
        return <div className="flex items-center" title={`Battery: ${level}%`}><Battery className={`${color} mr-1`} size={16} /><span>{level}%</span></div>;
    };

    const getSignalIndicator = (strength?: number, status?: Device['status']) => {
        if (status?.toLowerCase() === 'offline') return <div title="Offline"><WifiOff size={16} className="text-gray-400" /></div>;
        if (strength === undefined || strength === null) return <div title="Signal Unknown"><WifiOff size={16} className="text-gray-400" /></div>;
        const color = strength > 70 ? 'text-green-500' : strength > 30 ? 'text-yellow-500' : 'text-red-500';
        return <div className="flex items-center" title={`Signal: ${strength}%`}><Wifi className={`${color} mr-1`} size={16} /><span>{strength}%</span></div>;
    };

    const SortIndicator = ({ field }: { field: SortField }) => {
        if (field !== sortField) return null;
        return sortDirection === 'asc' ? <ChevronUp size={14} className="ml-1 opacity-70" /> : <ChevronDown size={14} className="ml-1 opacity-70" />;
    };

    // --- Live View Logic ---

    // Called when Camera icon is clicked
    const handleStartLiveView = (device: Device) => {
        console.log('Attempting to start live view for device:', device.id);

        // Don't start if already starting another one
        if (isStartingLiveView) {
            console.warn("Cannot start new live view while another is initializing.");
            return;
        }

        // Set device in state first to trigger Disclaimer or Progress
        setCurrentLiveView(prev => ({
            ...prev,
            device,
            connectionProgress: 0,
            liveViewData: null
        }));

        // Check if disclaimer needs to be shown
        const shouldShowDisclaimer = !localStorage.getItem('skip_liveview_disclaimer');
        setShowLiveViewDisclaimer(shouldShowDisclaimer);

        if (shouldShowDisclaimer) {
            console.log("Showing disclaimer first.");
            return;
        }

        // If disclaimer is skipped, proceed directly
        console.log("Disclaimer skipped, initiating connection...");
        initiateLiveViewConnection(device);
    };

    // Called after disclaimer confirmation OR if disclaimer is skipped
    const initiateLiveViewConnection = async (device: Device) => {
        // Double check if already starting (safety net)
        if (isStartingLiveView && currentRequestIdRef.current) {
            console.warn("Initiate called but already in progress for request:", currentRequestIdRef.current);
            return;
        }
        console.log(showLiveView);
        console.log(activeDevice);

        const vid = uuidv4(); // Generate unique request ID for this attempt
        currentRequestIdRef.current = vid; // Track this specific request
        setIsStartingLiveView(true); // Set loading flag (disables button)
        console.log(`Initiating connection for Device ${device.id} with Request ID: ${vid}`);

        // Update state to show progress modal
        setCurrentLiveView({
            device: device,
            connectionProgress: 20,
            liveViewData: null,
        });

        // try {
        //     console.log(`STEP 1 [${vid}]: Calling initialLiveView API`);
        //     if (!device.cid) {
        //         throw new Error(`Device ${device.id} is missing CID.`);
        //     }

        //     const response = await api.initiateLiveView(
        //         device.cid,
        //         vid,
        //         'liveview'
        //     );

        //     console.log(`STEP 2 [${vid}]: Live view API response received:`, response);

        //     // Check if this response is for the current request
        //     if (currentRequestIdRef.current !== vid) {
        //         console.warn(`Received API response for outdated request ${vid}. Current active request is ${currentRequestIdRef.current}. Ignoring response.`);
        //         return;
        //     }

        //     // Update progress
            setCurrentLiveView(prev => ({
                ...prev,
                device: prev.device?.id === device.id ? prev.device : device,
                connectionProgress: 60,showLiveView
            }));

        //     // Validate response structure and result
        //     if (!response || !response.result || !response.data || !response.data.credentials || !response.data.request_id) {
        //         console.error("Live view API error or invalid response structure:", response?.message || "Unknown API error", response);
        //         if (currentRequestIdRef.current === vid) {
        //             setCurrentLiveView({ device: null, connectionProgress: 0, liveViewData: null });
        //         }
        //         throw new Error(response?.message || "Failed to initialize live view from API");
        //     }

        //     console.log(`STEP 3 [${vid}]: API call successful. Preparing VisionMaxLiveView.`);
        //     console.log(`API Success - Data for Component [${vid}]:`, JSON.stringify(response.data));

        //     // Set final state to render VisionMaxLiveView
        //     if (response.data.request_id !== vid) {
        //         console.warn(`API response request_id (${response.data.request_id}) doesn't match generated vid (${vid}). Using response ID.`);
        //     }

        //     setCurrentLiveView(prev => ({
        //         device: prev.device?.id === device.id ? prev.device : device,
        //         connectionProgress: 100,
        //         liveViewData: {
        //             ...response.data,
        //             credentials: response.data.credentials,
        //             request_id: response.data.request_id || vid,
        //         }
        //     }));

        // } catch (error: any) {
        //     console.error(`Failed to start live view for request ${vid}:`, error);
        //     if (currentRequestIdRef.current === vid) {
        //         setCurrentLiveView({ device: null, connectionProgress: 0, liveViewData: null });
        //     }
        // } finally {
        //     if (currentRequestIdRef.current === vid) {
        //         setIsStartingLiveView(false);
        //         currentRequestIdRef.current = null;
        //         console.log(`Finished processing request ${vid}. Loading state reset.`);
        //     }
        // }
        
    };

    // Called by Modals or internal logic to close the live view sequence
    const handleCloseLiveView = () => {
        console.log("Closing live view.");
        setCurrentLiveView({
            device: null,
            connectionProgress: 0,
            liveViewData: null
        });
        setIsStartingLiveView(false);
        currentRequestIdRef.current = null;
    };

    // Handle actions from the Disclaimer modal
    const handleCloseDisclaimer = (proceed: boolean, dontShow: boolean) => {
        console.log(`Disclaimer closed. Proceed: ${proceed}, DontShowAgain: ${dontShow}`);
        setShowLiveViewDisclaimer(false);
        setDontShowAgain(dontShow);

        if (dontShow) {
            try { localStorage.setItem('skip_liveview_disclaimer', 'true'); } catch (e) { console.error("Failed to save disclaimer preference", e); }
        }

        if (proceed && currentLiveView.device) {
            initiateLiveViewConnection(currentLiveView.device);
        } else if (!proceed) {
            handleCloseLiveView();
        }
    };


    // --- Render Logic ---
    if (isLoading) {
        return <div className="w-full py-8"><div className="animate-pulse space-y-4">{[...Array(5)].map((_, i) => <div key={i} className="h-16 bg-gray-200 rounded"></div>)}</div></div>;
    }
    if (!devices || devices.length === 0) {
        return <div className="w-full py-8 text-center"><p className="text-gray-500">No devices found.</p></div>;
    }

    const activeDevice = currentLiveView.device;
    const showConnectionProgress = activeDevice && !showLiveViewDisclaimer && currentLiveView.connectionProgress > 0 && currentLiveView.connectionProgress < 100;
    const showLiveView = activeDevice && !showLiveViewDisclaimer;

    return (
        <div className="overflow-x-auto relative">
            <table className="min-w-full divide-y divide-gray-300">
                <thead className="bg-gray-50">
                    <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" onClick={() => handleSort('id')}>
                            <div className="flex items-center">Device ID <SortIndicator field="id" /></div>
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" onClick={() => handleSort('model')}>
                            <div className="flex items-center">Model <SortIndicator field="model" /></div>
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" onClick={() => handleSort('status')}>
                            <div className="flex items-center">Status <SortIndicator field="status" /></div>
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" onClick={() => handleSort('serialNumber')}>
                            <div className="flex items-center">Serial <SortIndicator field="serialNumber" /></div>
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" onClick={() => handleSort('lastSeen')}>
                            <div className="flex items-center">Last Seen <SortIndicator field="lastSeen" /></div>
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer" onClick={() => handleSort('adasHealth')}>
                            <div className="flex items-center">Health <SortIndicator field="adasHealth" /></div>
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Metrics</th>
                        <th scope="col" className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                    {sortedDevices.map((device) => {
                        const isDeviceOnline = device.status?.toLowerCase() === 'online';
                        const canStartLiveView = isDeviceOnline && !!device.cid;

                        return (
                            <tr key={device.id} className="hover:bg-gray-50">
                                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{device.id}</td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{device.model}</td>
                                <td className="px-6 py-4 whitespace-nowrap">{getStatusIndicator(device.status)}</td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{device.serialNumber}</td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{formatDate(device.lastSeen)}</td>
                                <td className="px-6 py-4 whitespace-nowrap">{getHealthBadge(device.adasHealth)}</td>
                                <td className="px-6 py-4 whitespace-nowrap">
                                    <div className="flex items-center space-x-3 text-sm text-gray-500">
                                        {getBatteryIndicator(device.batteryLevel)}
                                        {getSignalIndicator(device.signalStrength, device.status)}
                                    </div>
                                </td>
                                <td className="px-4 py-3 whitespace-nowrap text-center">
                                    <div className="flex justify-center items-center space-x-2">
                                        {/* Live View Button */}
                                        <button
                                            title={canStartLiveView ? "Start Live View" : (isDeviceOnline ? "Live View unavailable (Missing CID)" : "Device Offline")}
                                            className={`p-1 rounded-full transition-opacity duration-200 ${(!canStartLiveView || isStartingLiveView) ? 'opacity-40 cursor-not-allowed' : 'hover:bg-gray-200'}`}
                                            onClick={(e) => {
                                                e.stopPropagation(); // Prevent row click
                                                if (canStartLiveView) {
                                                   handleStartLiveView(device);
                                                }
                                            }}
                                            disabled={!canStartLiveView || isStartingLiveView}
                                        >
                                            <Camera size={18} className={canStartLiveView ? "text-blue-600" : "text-gray-400"} />
                                        </button>
                                        {/* Other Action Buttons */}
                                        <button title="Action 2 (Not Implemented)" className="p-1 rounded-full text-gray-400 opacity-40 cursor-not-allowed" disabled><Phone size={18} /></button>
                                        <button title="Action 3 (Not Implemented)" className="p-1 rounded-full text-gray-400 opacity-40 cursor-not-allowed" disabled><Monitor size={18} /></button>
                                    </div>
                                </td>
                            </tr>
                        );
                    })}
                </tbody>
            </table>

            {/* Pagination Placeholder */}
            <div className="flex justify-between items-center mt-4 px-4 py-2 border-t border-gray-200">
                <span className='text-sm text-gray-500'>Showing {devices.length} devices</span>
                <div className="flex space-x-2">
                    <button className="px-3 py-1 bg-gray-200 text-gray-700 rounded text-sm hover:bg-gray-300 disabled:opacity-50" disabled>&lt;</button>
                    <button className="px-3 py-1 bg-gray-200 text-gray-700 rounded text-sm hover:bg-gray-300 disabled:opacity-50" disabled>&gt;</button>
                </div>
            </div>

            {/* --- Modals --- */}
            {activeDevice && showLiveViewDisclaimer && (
                <DisclaimerModal
                    dontShowAgain={dontShowAgain}
                    onDontShowAgainChange={setDontShowAgain}
                    onCancel={() => handleCloseDisclaimer(false, dontShowAgain)}
                    onConfirm={() => handleCloseDisclaimer(true, dontShowAgain)}
                />
            )}

            {showConnectionProgress && activeDevice && (
                <ConnectionProgressModal
                    deviceId={activeDevice.serialNumber || activeDevice.id}
                    progress={currentLiveView.connectionProgress}
                    onClose={handleCloseLiveView}
                />
            )}

            {/* Use our new VisionMaxLiveView component instead of LiveViewModal */}
            {showLiveView && activeDevice && (
                <VisionMaxLiveView
                    device={activeDevice}
                    onError={(error) => {
                        console.error("VisionMaxLiveView reported error:", error);
                        // Current VisionMaxLiveView handles retry internally
                    } }
                    onClose={handleCloseLiveView}
                    modalStyle={true}
                    title={`Live View - ${activeDevice.serialNumber || activeDevice.id}`} 
              />
            )}
        </div>
    );
};

export default DevicesTable;