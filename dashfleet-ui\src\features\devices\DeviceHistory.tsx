// import React, { useState, useEffect } from 'react';
// import { useNavigate } from 'react-router-dom';
// import { format, subDays } from 'date-fns';
// import { AlertTriangle, MapPin } from 'lucide-react';

// import { Card, CardContent, CardHeader, CardTitle } from '../../components/common/Card';
// import { Button } from '../../components/common/Button';
// import { Spinner } from '../../components/common/Spinner';
// import { Alert } from '../../components/common/Alert';
// import { TripStatsChart } from '../../components/charts/TripStatsChart';
// import { DataFilter } from '../../components/common/DataFilter';
// import { EventsDistributionChart } from '../../components/charts/EventDistributionChart';

// import { VisionMaxAPI } from '../../api/visionMaxApi';

// interface DeviceHistoryProps {
//   deviceId: string;
// }

// interface TripStatItem {
//   date: string;
//   trips: number;
//   distance: number;
//   duration: number;
// }

// interface HistoryEvent {
//   id: string;
//   type: string;
//   timestamp: string;
//   description?: string;
//   location?: string;
// }

// interface DeviceHistoryData {
//   events: HistoryEvent[];
//   trips: any[];
//   tripStats: TripStatItem[];
// }

// export const DeviceHistory: React.FC<DeviceHistoryProps> = ({ deviceId }) => {
//   const navigate = useNavigate();
  
//   // Filter state
//   const [dateRange, setDateRange] = useState({
//     startDate: subDays(new Date(), 30), // Last 30 days
//     endDate: new Date(),
//   });
  
//   const [eventType, setEventType] = useState<string[]>([]);
  
//   // History data state
//   const [history, setHistory] = useState<DeviceHistoryData | null>(null);
//   const [isLoading, setIsLoading] = useState(true);
//   const [error, setError] = useState<Error | null>(null);
  
//   // Create API instance
//   const token = localStorage.getItem('token');
//   const api = new VisionMaxAPI({
//     baseURL: process.env.REACT_APP_API_BASE_URL || 'https://api.visionmaxfleet.com',
//     token
//   });
  
//   // Fetch history data
//   const fetchHistory = async () => {
//     setIsLoading(true);
//     setError(null);
    
//     try {
//       // Convert dates to unix timestamps
//       const startTimestamp = Math.floor(dateRange.startDate.getTime() / 1000);
//       const endTimestamp = Math.floor(dateRange.endDate.getTime() / 1000);
      
//       // Filter event types if specified
//       const eventTypeFilter = eventType.length > 0 ? eventType.join(',') : undefined;
      
//       // Fetch events
//       const eventsResponse = await api.getEvents({
//         since: startTimestamp,
//         until: endTimestamp,
//         keyword: deviceId, // Use the device ID to filter events
//         type: eventTypeFilter,
//         limit: 100,
//         offset: 0
//       });
      
//       // Fetch trip statistics
//       const tripsResponse = await api.getTripStatistics({
//         since: startTimestamp,
//         until: endTimestamp,
//         mode: 'day',
//         tz: Intl.DateTimeFormat().resolvedOptions().timeZone
//       });
      
//       // Format data for components
//       const formattedEvents = eventsResponse.data.events.map(event => ({
//         id: event.id,
//         type: event.name,
//         timestamp: new Date(event.time * 1000).toISOString(),
//         description: `${event.severity_level} ${event.name}`,
//         location: `${event.lat}, ${event.lng}`
//       }));
      
//       // Create trip stats data
//       const tripStats: TripStatItem[] = [
//         {
//           date: format(dateRange.startDate, 'yyyy-MM-dd'),
//           trips: tripsResponse.data.trip_count || 0,
//           distance: tripsResponse.data.distance || 0,
//           duration: (tripsResponse.data.duration || 0) / 3600 // Convert seconds to hours
//         }
//       ];
      
//       setHistory({
//         events: formattedEvents,
//         trips: [], // We don't have detailed trip data in this API
//         tripStats
//       });
      
//     } catch (err) {
//       console.error('Error fetching device history:', err);
//       setError(err instanceof Error ? err : new Error('Failed to fetch history data'));
//     } finally {
//       setIsLoading(false);
//     }
//   };
  
//   // Fetch data when filters change
//   useEffect(() => {
//     fetchHistory();
//   }, [deviceId, dateRange.startDate, dateRange.endDate, eventType.join(',')]);
  
//   // Handle date range change
//   const handleDateRangeChange = (startDate: Date, endDate: Date) => {
//     setDateRange({ startDate, endDate });
//   };
  
//   // Handle filter change
//   const handleFilterChange = (filterId: string, values: string[]) => {
//     if (filterId === 'eventType') {
//       setEventType(values);
//     }
//   };
  
//   // Handle clear filters
//   const handleClearFilters = () => {
//     setEventType([]);
//   };
  
//   // Format date time
//   const formatDateTime = (dateString: string) => {
//     try {
//       return format(new Date(dateString), 'MMM d, yyyy h:mm a');
//     } catch (error) {
//       return dateString;
//     }
//   };
  
//   // Get event type icon and color
//   const getEventTypeStyles = (type: string) => {
//     switch (type.toLowerCase()) {
//       case 'hardbrake':
//       case 'hardbraking':
//         return { color: 'bg-red-100 text-red-800', icon: <AlertTriangle size={16} className="text-red-500" /> };
//       case 'acceleration':
//       case 'hardacceleration':
//         return { color: 'bg-orange-100 text-orange-800', icon: <AlertTriangle size={16} className="text-orange-500" /> };
//       case 'speeding':
//         return { color: 'bg-yellow-100 text-yellow-800', icon: <AlertTriangle size={16} className="text-yellow-500" /> };
//       case 'turn':
//       case 'harshturn':
//         return { color: 'bg-blue-100 text-blue-800', icon: <AlertTriangle size={16} className="text-blue-500" /> };
//       case 'collision':
//         return { color: 'bg-purple-100 text-purple-800', icon: <AlertTriangle size={16} className="text-purple-500" /> };
//       default:
//         return { color: 'bg-gray-100 text-gray-800', icon: <AlertTriangle size={16} className="text-gray-500" /> };
//     }
//   };
  
//   // Filter groups
//   const filterGroups = [
//     {
//       id: 'eventType',
//       label: 'Event Type',
//       options: [
//         { id: 'hardBrake', label: 'Hard Braking' },
//         { id: 'acceleration', label: 'Hard Acceleration' },
//         { id: 'speeding', label: 'Speeding' },
//         { id: 'turn', label: 'Harsh Turn' },
//         { id: 'collision', label: 'Collision' },
//       ],
//     },
//   ];
  
//   // Loading state
//   if (isLoading) {
//     return (
//       <div className="flex justify-center items-center p-8">
//         <Spinner size="large" color="primary" />
//       </div>
//     );
//   }
  
//   // Error state
//   if (error) {
//     return (
//       <Alert
//         type="error"
//         title="Error Loading History"
//         message="There was a problem loading the device history. Please try again."
//         action={{
//           label: 'Retry',
//           onClick: fetchHistory
//         }}
//       />
//     );
//   }
  
//   return (
//     <div className="space-y-6">
//       <Card>
//         <CardHeader>
//           <CardTitle>History Filters</CardTitle>
//         </CardHeader>
//         <CardContent>
//           <DataFilter
//             dateRange={dateRange}
//             filters={filterGroups}
//             activeFilters={{ eventType }}
//             onDateRangeChange={handleDateRangeChange}
//             onFilterChange={handleFilterChange}
//             onClearFilters={handleClearFilters}
//           />
//         </CardContent>
//       </Card>
      
//       <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
//         <Card>
//           <CardHeader>
//             <CardTitle>Trip Statistics</CardTitle>
//           </CardHeader>
//           <CardContent>
//             {history?.tripStats && history.tripStats.length > 0 ? (
//               <TripStatsChart data={history.tripStats} />
//             ) : (
//               <div className="flex items-center justify-center h-48 text-gray-500">
//                 No trip data available for this period
//               </div>
//             )}
//           </CardContent>
//         </Card>
        
//         <Card>
//           <CardHeader>
//             <CardTitle>Event Distribution</CardTitle>
//           </CardHeader>
//           <CardContent>
//             {history?.events && history.events.length > 0 ? (
//               <EventsDistributionChart
//                 data={[
//                   {
//                     type: 'Hard Braking',
//                     count: history.events.filter(e => e.type.toLowerCase().includes('brake')).length,
//                     color: '#EF4444',
//                   },
//                   {
//                     type: 'Speeding',
//                     count: history.events.filter(e => e.type.toLowerCase().includes('speed')).length,
//                     color: '#F97316',
//                   },
//                   {
//                     type: 'Hard Acceleration',
//                     count: history.events.filter(e => e.type.toLowerCase().includes('acceleration')).length,
//                     color: '#F59E0B',
//                   },
//                   {
//                     type: 'Harsh Turn',
//                     count: history.events.filter(e => e.type.toLowerCase().includes('turn')).length,
//                     color: '#10B981',
//                   },
//                   {
//                     type: 'Other',
//                     count: history.events.filter(e => 
//                       !['brake', 'speed', 'acceleration', 'turn'].some(term => 
//                         e.type.toLowerCase().includes(term)
//                       )
//                     ).length,
//                     color: '#6B7280',
//                   },
//                 ]}
//               />
//             ) : (
//               <div className="flex items-center justify-center h-48 text-gray-500">
//                 No event data available for this period
//               </div>
//             )}
//           </CardContent>
//         </Card>
//       </div>
      
//       <Card>
//         <CardHeader>
//           <CardTitle>Event History</CardTitle>
//         </CardHeader>
//         <CardContent>
//           {history?.events && history.events.length > 0 ? (
//             <div className="relative">
//               {/* Timeline line */}
//               <div className="absolute left-2.5 top-6 bottom-6 w-px bg-gray-200"></div>
              
//               <div className="space-y-6">
//                 {history.events.map(event => {
//                   const { color, icon } = getEventTypeStyles(String(event.type));
                  
//                   return (
//                     <div key={event.id} className="flex items-start relative pl-10">
//                       <div className={`absolute left-0 w-5 h-5 rounded-full ${color.split(' ')[0]} flex items-center justify-center z-10`}>
//                         {icon}
//                       </div>
//                       <div className="flex-1 bg-gray-50 rounded-lg p-4">
//                         <div className="flex justify-between items-start">
//                           <div>
//                             <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${color}`}>
//                               {event.type}
//                             </span>
//                             <div className="text-sm text-gray-500 mt-1">
//                               {formatDateTime(event.timestamp)}
//                             </div>
//                           </div>
//                           <Button 
//                             variant="ghost" 
//                             size="sm"
//                             onClick={() => navigate(`/events/${event.id}`)}
//                           >
//                             View
//                           </Button>
//                         </div>
//                         {event.description && (
//                           <div className="mt-2 text-sm text-gray-700">
//                             {event.description}
//                           </div>
//                         )}
//                         {event.location && (
//                           <div className="mt-2 text-xs text-gray-500 flex items-center">
//                             <MapPin size={12} className="mr-1" />
//                             {event.location}
//                           </div>
//                         )}
//                       </div>
//                     </div>
//                   );
//                 })}
//               </div>
//             </div>
//           ) : (
//             <div className="py-8 text-center text-gray-500">
//               No events found for this period
//             </div>
//           )}
//         </CardContent>
//       </Card>
//     </div>
//   );
// };

// export default DeviceHistory;