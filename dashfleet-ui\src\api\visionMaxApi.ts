/**
 * VisionMax API Client
 * 
 * This file contains TypeScript interfaces and functions for interacting with the VisionMax Fleet API.
 * Generated from swagger.json documentation.
 */

import axios, { AxiosInstance } from 'axios';

// Base API configuration
export interface ApiConfig {
  baseURL: string;
  apiKey?: string;
  token?: string;
}

// Common types
export enum Order {
  ASC = 'ASC',
  DESC = 'DESC'
}

export enum UserStatus {
  Pending = 0,
  Active = 1,
  Deactive = 2
}

export interface BaseResponse {
  status: string;
  code: number;
  message: string;
}

// Authentication interfaces
export interface AuthRequest {
  email: string;
  password: string;
}

export interface AuthResponse extends BaseResponse {
  data: {
    token: string;
    user_info: {
      id: number;
      email: string;
      name: string;
      role: number;
      role_name: string;
      fleets?: Array<{
        id: number;
        name: string;
      }>;
    };
  };
}

export interface FleetTokenSwitchResponse extends BaseResponse {
  data: {
    token: string;
    fleets?: Array<{
      id: number;
      name: string;
    }>;
  };
}

// Device interfaces
export interface DevicesResponse extends BaseResponse {
  data: {
    items: DeviceInfo[];
    total: number;
  };
}

export interface DeviceInfo {
  id: string;
  cid: string;
  name: string;
  serialNumber: string;
  imei1: string;
  imei2: string;
  appVersion: string;
  firmwareVersion: string;
  baseImageVersion: string;
  regionImageVersion: string;
  aiRegion: string;
  status: string;
  activatedSince: string;
  activatedFrom: string;
  firstConnectedTimestamp: number;
  installPosition: string;
  frontCameraHeight: number;
  groupId: number;
  vehicleTypeId: number;
  vehicleTypeCode: string;
  vehicleTypeName: string;
  licensePlate: string;
  vin: string;
  model: string;
  adasStatus: string;
  dmsStatus: string;
  planType: string;
}

export interface DeviceUpdateRequest {
  cid: string;
  name?: string;
  license_plate?: string;
  vin?: string;
  vehicle_type_id?: number;
  ai_region?: string;
  front_camera_height?: number;
  group_id?: number;
}

export interface HealthReportResponse extends BaseResponse {
  data: {
    cid: string;
    device_id: number;
    model: string;
    name: string;
    repair_flag: boolean;
    attention_flag: boolean;
    assigning_status: boolean;
    report: any; // This is a complex JSON structure
  };
}

export interface HealthSimpleReportResponse extends BaseResponse {
  data: {
    cid: string;
    device_id: number;
    model: string;
    name: string;
    repair_flag: boolean;
    attention_flag: boolean;
    assigning_status: boolean;
    reports: Array<{
      category: string;
      title: string;
      status: string;
      description: string;
      suggestion: string;
      priority: number;
    }>;
  };
}

export interface DeviceStorageListResponse extends BaseResponse {
  data: {
    storage_list: Array<{
      storage_name: string;
      total_space: number;
      free_space: number;
      used_space: number;
      used_percentage: number;
    }>;
  };
}

export interface FormatSdCardToDeviceRequest {
  force?: boolean;
}

export interface DeleteSDFileRequest {
  path: string;
}

export interface CancelTaskRequest {
  task_id: string;
}

export interface ListTaskRequest {
  task_ids?: string[];
  task_types?: string[];
  start_time?: number;
  end_time?: number;
}

export interface ListTaskResponse extends BaseResponse {
  data: {
    total_count: number;
    page_size: number;
    page: number;
    items: Array<{
      task_id: string;
      task_type: string;
      device_sn: string;
      state: string;
      code: string;
      desc: string;
      created_at: number;
      updated_at: number;
      data: any;
    }>;
  };
}

export interface MqttCommandResponse extends BaseResponse {
  data: {
    task_id: string;
  };
}

// Trip interfaces
export interface TripStatisticsResponse extends BaseResponse {
  data: {
    trip_count: number;
    distance: number;
    idle_time: number;
    duration: number;
  };
}

export interface FleetAvgPerDriverDayResponse extends BaseResponse {
  data: {
    avg_distance_per_day: number;
    avg_idle_time_per_day: number;
    avg_duration_per_day: number;
    avg_trips_per_day: number;
    active_driver_count: number;
  };
}

export interface DriverTripSummaryResponse extends BaseResponse {
  data: {
    items: Array<{
      driver_id: number;
      driver_name: string;
      asset_id: string;
      trip_count: number;
      distance: number;
      idle_time: number;
      duration: number;
    }>;
    total: number;
  };
}
export interface TripQueryParams {
  offset?: number;
  limit?: number;
  sort?: string;
  cid?: string;
  keyword?: string;
  page?: number;
  pageSize?: number;
  since?: number;
  until?: number;
  order?: Order;
  status?: string[];
}

export interface TripReportFleetAvgResponse {
  data: {
    avg_trips_per_driver_day: number | null;
    avg_distance_per_driver_day: number | null;
    avg_idling_per_driver_day: number | null;
    avg_time_per_driver_day: number | null;
  };
  result: boolean;
  code: string;
  message: string;
  doc_version: string;
  uq: string;
  build: string;
}

export interface TripReportDriverSummaryResponse {
  data: Array<{
    driver_id: number;
    driver_name: string;
    asset_id: string;
    trip_count: number;
    distance: number;
    idle_time: number;
    duration: number;
  }>;
  result: boolean;
  code: string;
  message: string;
  doc_version: string;
  uq: string;
  build: string;
}

export interface TripReportStatisticsResponse {
  data: {
    dates: {
      [date: string]: {
        total_trips: number;
        total_distance: number;
        total_idling: number;
        total_time: number;
      };
    };
  };
  result: boolean;
  code: string;
  message: string;
  doc_version: string;
  uq: string;
  build: string;
}

// Trip Report Query Parameters
export interface TripReportParams {
  since: number;
  until: number;
  tz?: string;
  sort?: string;
  order?: Order;
  mode?: string;
}

export interface TripsResponse extends BaseResponse {
 count: number;
  data: Array<{
    id: number;
    device_name: string;
    cid: string;
    mileage: number;
    start_timestamp: number;
    end_timestamp: number;
    is_new: boolean;
    clicked_count: number;
    status: string; // "Live" | "Finish"
    invalid_ic_card: string | null;
    events: number;
    excluded_events: string;
    duration: number | string; // Can be "---" for live trips or number for finished
    headshots_event_id: number;
    clock_in_status: {
      nfc_clock_in: boolean;
      headshot: boolean;
      recognized: boolean;
      driver_avatar: boolean;
      headshot_image_url: string;
      driver_avatar_url: boolean | string;
    };
    driver_id: number | null;
    driver_status: string | null;
    driver: any | null;
    driver_name: string | null;
    driver_avatar: string | null;
  }>;
  result: boolean;
  doc_version: string;
  uq: string;
  build: string;
}

export interface TripDetailResponse extends BaseResponse {
  data: {
    id: number;
    status: string;
    asset_id: string;
    driver_id: number;
    driver_name: string;
    driver_avatar: string;
    start_time: number;
    end_time: number;
    event_count: number;
    mileage: number;
    duration: number;
    gps: {
      lat: number;
      lng: number;
      alt: string;
      direction: string;
      speed: string;
    };
  };
}

// Events interfaces
export interface EventsResponse extends BaseResponse {
  data: {
    events: Array<{
      id: string;
      ticket: string;
      type: number;
      name: string;
      severity_level: string;
      time: number;
      driver_id: number;
      driver_name: string;
      asset_id: string;
      lat: string;
      lng: string;
      snapshots: {
        count: number;
        c_modes: string[];
      };
      video: {
        count: number;
        c_modes: string[];
      };
      position: {
        lat: number;
        lng: number;
      };
      timestamp: string;
    }>;
    total: number;
  };
}

export interface EventDetailInfosResponse extends BaseResponse {
  data: {
    ticket: string;
    event: {
      id: number;
      type: number;
      name: string;
      severity_level: string;
      time: number;
      driver_id: number;
      driver_name: string;
      driver_avatar: string;
      asset_id: string;
      lat: string;
      lng: string;
      speed: string;
      address: string;
      nfc?: {
        id: string;
      };
    };
    assets: {
      snapshots: Array<{
        c_mode: string;
        snapshot_url: string;
        thumbnail_url: string;
      }>;
      video: Array<{
        c_mode: string;
        video_url: string;
        thumbnail_url: string;
      }>;
    };
    trip: {
      id: number;
      status: string;
      start_time: number;
      end_time: number;
      mileage: number;
      duration: number;
    };
  };
}

export interface VideoDetailInfoResponse {
  data: {
    ticket: string;
    id: number;
    type: number;
    duration: number;
    device_name: string;
    event_timestamp: number;
    timestamp: number;
    start_timestamp: number;
    end_timestamp: number;
    video_triggered_timestamp: number;
    video_start_timestamp: number;
    video_end_timestamp: number;
    title: string | null;
    ticket_linked: string | null;
    snapshots: Array<{
      ticket: string;
      url: string;
      capture_mode: string;
    }>;
    videos: Array<{
      video_id: number;
      video_url: string;
      screenshot_url: string;
      origin_file_name: string;
      general_format: string;
      general_filesize: number;
      general_duration: number;
      video_streamorder: number;
      video_codecid: string;
      video_duration: number;
      video_bitrate: number;
      video_width: number;
      video_height: number;
      video_framerate: number;
      video_colorspace: string;
      capture_mode: string;
      video_start_at: number;
      video_end_at: number;
    }>;
    expiry: number;
    nmea_url: string;
    organization_id: number;
    event_assets: {
      capture_mode: {
        snapshot: string[];
        video: string[];
      };
      total_snapshots: number;
      total_videos: number;
    };
    video_uploaded: number;
    videos_total_count: number;
    videos_count: number;
  };
  result: boolean;
  code: string;
  message: string;
  doc_version: string;
  uq: string;
  build: string;
}

export interface StorageListResponse extends BaseResponse {
  data: {
    items: Array<{
      ticket: string;
      type: number;
      name: string;
      time: number;
      driver_id: number;
      driver_name: string;
      driver_avatar: string;
      cid: string;
      asset_id: string;
      lat: string;
      lng: string;
      expire_status: boolean;
      left_days: number;
      assets: {
        snapshots: Array<{
          c_mode: string;
          snapshot_url: string;
          thumbnail_url: string;
        }>;
        video: Array<{
          c_mode: string;
          video_url: string;
          thumbnail_url: string;
        }>;
      };
      tag_info: string;
      trip_id: number;
    }>;
    total: number;
  };
}

export interface GetVideoClipRequest {
  video_clip_type: string;
  video_format?: string;
  start_time: number;
  end_time: number;
  tag?: string;
}

export interface GetVideoClipResponse extends BaseResponse {
  data: {
    task_id: string;
  };
}

export interface TaskProcessResultResponse extends BaseResponse {
  data: {
    task_id: string;
    device_sn: string;
    state: string;
    code: string;
    desc: string;
    report_at: number;
  };
}
export interface VehicleType {
  id: number;
  type: string;
  is_default: number;
  display_name: string;
}

// Device interface for the new response
export interface VehicleDevice {
  id: number;
  name: string;
}

export interface Vehicle {
  vehicle_id: number;
  vehicle_name: string;
  vin: string;
  year: number;
  make: string;
  model: string;
  type: VehicleType;
  license_plate: string;
  odometer: number;
  status: number;
  device: VehicleDevice | null;
  groups: any[]; // Array of groups, you can define a specific type if needed
}


// Vehicle interfaces
export interface VehicleListResponse {
  data: Vehicle[]; // Direct array
  total: number;
  result: boolean;
  code: string;
  message: string;
  doc_version: string;
  uq: string;
  build: string;
}

export interface VehiclesDetail {
  id: number;
  license_plate: string;
  vin: string;
  model: string;
  paired_device: {
    id: number;
    name: string;
    serial_number: string;
  } | null;
  status: number;
  created_at: number;
  updated_at: number;
}

export interface AddVehicleRequest {
  vehicle_name: string;
  vin: string;
  year: number;
  make: string;
  model: string;
  type: number;
  license_plate: string;
  odometer_unit: string;
  groups: any[];
  odometer?: number; // Optional field
}

export interface SetVehicleOfDeviceRequest {
  device_id: number;
  vehicle_id: number;
}

export interface AvailableDevicesResponse {
  data: Array<{
    id: number;
    name: string;
  }>;
  result: boolean;
  code: string;
  message: string;
  doc_version: string;
  uq: string;
  build: string;
}

// Group interfaces
export interface GroupResponse extends BaseResponse {
  data: {
    items: Array<{
      id: number;
      name: string;
      asset_count: number;
    }>;
  };
}

export interface CheckVehicleAttributesRequest {
  checkVickeName: {
    vehicle_name?: string;
    vin?: string;
  };
}

export interface CheckVehicleAttributesResponse {
  data: {
    checkVickeName: {
      result: boolean;
      vehicle_name?: Array<{
        message: string;
        code: string;
      }>;
      vin?: Array<{
        message: string;
        code: string;
      }>;
    };
  };
  result: boolean;
  code: string;
  message: string;
  doc_version: string;
  uq: string;
  build: string;
}


export interface AvailableGroupsResponse {
  data: Array<{
    id: number;
    name: string;
  }>;
  result: boolean;
  code: string;
  message: string;
  doc_version: string;
  uq: string;
  build: string;
}

export interface ListGroupResponse extends BaseResponse {
  data: {
    groups: Array<{
      id: number;
      name: string;
      assets: Array<{
        id: number;
        cid: string;
        name: string;
        serialNumber: string;
        imei1: string;
        imei2: string;
        status: string;
        appVersion: string;
        firmwareVersion: string;
        baseImageVersion: string;
        regionImageVersion: string;
        aiRegion: string;
        vehicleTypeCode: string;
        vehicleTypeName: string;
        licensePlate: string;
        vin: string;
        model: string;
        adasStatus: string;
        dmsStatus: string;
        planType: string;
      }>;
    }>;
  };
}

// ODB2 interfaces
export interface GetDeviceOdb2Response extends BaseResponse {
  data: {
    items: Array<{
      timestamp: number;
      total_vehicle_distance?: number;
      total_fuel_used?: number;
      total_hours_of_operation?: number;
      vehicle_speed?: number;
      engine_speed?: number;
      engine_fuel_rate?: number;
      accelerator_pedal_position_d?: number;
      fuel_level?: number;
      battery_voltage?: number;
      accelerator_pedal_position_e?: number;
      cruise_control?: number;
      brake_pedal_position?: number;
      total_trip_harsh_brakes?: number;
      total_trip_harsh_acceleration_time?: number;
      total_trip_acceleration_time?: number;
      total_trip_acceleration_distance?: number;
      total_trip_deceleration_time?: number;
      total_trip_over_speed_time?: number;
      total_trip_over_speed_distance?: number;
      retarder_info_total_time?: number;
      retarder_info_total_distance?: number;
      turn_right_lights?: number;
      turn_left_lights?: number;
      turn_head_lights?: number;
      driver_seat_belt?: number;
      gear_position?: number;
      malfunction_indicator_lamp?: number;
      engine_load?: number;
      engine_temperature?: number;
      instant_fuel_consumption?: number;
      battery_soc?: number;
      battery_soh?: number;
      battery_current?: number;
      license_status?: number;
      license_expiration_time?: number;
      acc_ignition?: number;
      accelerator_pedal_position_f?: number;
    }>;
  };
}

// SetUp interfaces
export interface SetUpBaseResponse extends BaseResponse {
  data: {
    task_id: string;
  };
}

// Webhook interfaces
export interface ListWebhookResponse extends BaseResponse {
  data: {
    hooks: string[];
  };
}

export interface WebhookSubscribeRequest {
  hook_name: string;
  url: string;
}

export interface WebhookUnscribeRequest {
  hook_name: string;
}

export interface VmxBaseResponse {
  status: string;
  code: number;
  message: string;
}

// Driver interfaces
export interface NewDriverResponse {
  count: number;
  data: DriverInfo[];
  result: boolean;
  code: string;
  message: string;
  doc_version: string;
  uq: string;
  build: string;
}

export interface DriverInfo {
  id: number;
  driver_id: number;
  name: string;
  employee_id: string;
  rfid: string;
  email: string;
  phone: number;
  enabled: number;
  avatar: string | null;
  // Additional fields that might be in other responses
  status?: string;
  vehicleType?: string;
  licensePlate?: string;
  licenseType?: string;
  lastActive?: string;
}

export interface CreateDriverRequest {
  name: string;
  employee_id: string;
  rfid: string;
  email: string;
  phone: string;
  logo?: File | null;
}

export interface CreateDriverResponse {
    result: boolean,
    message: string;
    code: string;
    doc_version: string;
    uq: string;
    build: string;  
}
// Original driver response format - keeping for backward compatibility
export interface GetAllDriverResponse extends BaseResponse {
  data: {
    items: Array<{
      status: string;
      id: number;
      name: string;
      employee_id: string;
      email: string;
      phone: string;
      rfid: string;
      avatar: string;
    }>;
    total: number;
  };
} 

// // Configuration interfaces
// export interface ConfigurationResponse extends BaseResponse {
//   data: {
//     vehicle_types: Array<{
//       id: number;
//       name: string;
//       code: string;
//       events: Array<{
//         id: number;
//         name: string;
//         pre_event_length: number;
//         post_event_length: number;
//         videoOn: boolean;
//         gpxOn: boolean;
//       }>;
//       video_format: {
//         c_mode: string;
//         format: string;
//       };
//     }>;
//   };
// }

export interface UpdatecConfigurationRequest {
  vehicle_types: Array<{
    id: number;
    events?: Array<{
      id: number;
      pre_event_length?: number;
      post_event_length?: number;
      videoOn?: boolean;
      gpxOn?: boolean;
    }>;
    video_format?: {
      c_mode: string;
      format: string;
    };
  }>;
}

export interface DeviceConfigurationResponse extends BaseResponse {
  data: {
    cid: string;
    device_id: number;
    device_name: string;
    vehicle_type: {
      id: number;
      name: string;
      code: string;
    };
    events: Array<{
      id: number;
      name: string;
      pre_event_length: number;
      post_event_length: number;
      videoOn: boolean;
      gpxOn: boolean;
    }>;
    video_format: {
      c_mode: string;
      format: string;
    };
  };
}

export interface ConfigSet {
  events?: Array<{
    id: number;
    pre_event_length?: number;
    post_event_length?: number;
    videoOn?: boolean;
    gpxOn?: boolean;
  }>;
  video_format?: {
    c_mode: string;
    format: string;
  };
}



// Configuration interfaces - NEW SECTION based on API response
export interface DeviceSettings {
  system_soundVolume: number;
  system_speedUnit: string;
  system_cabinCameraLocationSide: string;
  system_cabinCameraLocationWhere: string;
  system_parkingPhoto: string;
  system_alertLanguage: string;
  system_rfid: boolean;
  privacy_mode_allowed: boolean;
  system_btn1_voice_call_enable: boolean;
  system_sdcard_encrypted: boolean;
  system_camera_height_auto_detect: boolean;
  system_privacy_mode_on: boolean;
  system_acc_off_debounce: number;
  system_acc_off_keep_record_second: number;
  system_ai_extra_info: boolean;
  vr_recordSound: boolean;
  vr_speedCoordinateStamp: boolean;
  vr_videoSnapshots: boolean;
  vr_videoFormat: string;
  vr_videoResolution: string;
  vr_evIn: number;
  vr_evOut: number;
  vr_K145_camera_height: number;
  vr_K245_camera_height: number;
  vr_K220_camera_height: number;
  vr_K165_camera_height: number;
  vr_K265_camera_height: number;
  vr_T10_camera_height: number;
  vr_inwardCamEnable: boolean;
  vr_inward_cam_privacy_mode: boolean;
  [key: string]: any; // For any other device settings
}

export interface SensorSettings {
  impactEnable: boolean;
  harshEnable: boolean;
  harsh_haEnable: boolean;
  harsh_hbEnable: boolean;
  harsh_hcEnable: boolean;
  impact_drivingSensitive: number;
  impact_parkingSensitive: number;
  impact_videoBefore: number;
  impact_videoAfter: number;
  impact_media: string;
  impact_gsensor: boolean;
  impact_emailNotify: boolean;
  impact_audioAlert: boolean;
  harsh_haSpeed: number;
  harsh_hbSpeed: number;
  harsh_hcSpeed: number;
  harsh_haSensitive: number;
  harsh_hbSensitive: number;
  harsh_hcSensitive: number;
  harsh_videoBefore: number;
  harsh_videoAfter: number;
  harsh_minorMedia: string | null;
  harsh_minorGsensor: boolean;
  harsh_moderateMedia: string | null;
  harsh_moderateGsensor: boolean;
  harsh_severeMedia: string | null;
  harsh_severeGsensor: boolean;
  harsh_emailNotify: string;
  harsh_audioAlert: boolean;
  harsh_haAudioAlert: boolean;
  harsh_hbAudioAlert: boolean;
  harsh_hcAudioAlert: boolean;
  [key: string]: any; // For any other sensor settings
}

export interface AIEventDetectionSettings {
  adasEnable: boolean;
  adas_frontalEnable: boolean;
  adas_tailgatingEnable: boolean;
  adas_laneEnable: boolean;
  adas_stopandgoEnable: boolean;
  adas_pedestrianCollisionEnable: boolean;
  adas_autoCalibrationEnable: boolean;
  dmsEnable: boolean;
  dms_fatigueEnable: boolean;
  dms_distractionEnable: boolean;
  dms_phoneEnable: boolean;
  dms_seatbeltUnfastenEnable: boolean;
  dms_lensCoveredEnable: boolean;
  frontal_threshold: number;
  frontal_sensitive: number;
  frontal_videoBefore: number;
  frontal_videoAfter: number;
  frontal_media: string;
  frontal_gsensor: boolean;
  frontal_emailNotify: boolean;
  frontal_audioAlert: boolean;
  frontal_workSpeedMin: number;
  frontal_workSpeedMax: number;
  [key: string]: any; // For other AI event detection settings
}

export interface SpeedingSettings {
  speedingEnable: boolean;
  truckModeEnable: boolean;
  speeding_threshold: number;
  speeding_videoBefore: number;
  speeding_videoAfter: number;
  speeding_minorMedia: string;
  speeding_minorGsensor: boolean;
  speeding_moderateMedia: string;
  speeding_moderateGsensor: boolean;
  speeding_severeMedia: string;
  speeding_severeGsensor: boolean;
  speeding_emailNotify: string;
  speeding_audioAlert: boolean;
  truckMode_MMF: string;
  mmfOverSpeed_audioAlert: string;
}

export interface TrafficAndSignSettings {
  speedCamEnable: boolean;
  speedCam_audioAlert: boolean;
  speedCam_audioPreWarning: boolean;
  speedCam_videoBefore: number;
  speedCam_videoAfter: number;
  speedCam_minorMedia: string;
  speedCam_minorGsensor: boolean;
  speedCam_moderateMedia: string;
  speedCam_moderateGsensor: boolean;
  speedCam_severeMedia: string;
  speedCam_severeGsensor: boolean;
  speedCam_emailNotify: string;
  speedCam_speed: number;
  rollingStopEnable: boolean;
  railroadCrossEnable: boolean;
  schoolZoneSpeedingEnable: boolean;
  speedLimitEnable: boolean;
  [key: string]: any; // For other traffic and sign settings
}

export interface ScoreSettings {
  impactValue: number;
  parkingImpactValue: number;
  haValue: number;
  haMinorWeight: number;
  haModerateWeight: number;
  haSevereWeight: number;
  hbValue: number;
  hbMinorWeight: number;
  hbModerateWeight: number;
  hbSevereWeight: number;
  hcValue: number;
  hcMinorWeight: number;
  hcModerateWeight: number;
  hcSevereWeight: number;
  frontalValue: number;
  tailgatingValue: number;
  laneValue: number;
  stopandgoValue: number;
  fatigueValue: number;
  distractionValue: number;
  phoneValue: number;
  speedingValue: number;
  speedingMinorWeight: number;
  speedingModerateWeight: number;
  speedingSevereWeight: number;
  speedCameraValue: number;
  speedCameraMinorWeight: number;
  speedCameraModerateWeight: number;
  speedCameraSevereWeight: number;
  rollingStopValue: number;
}

export interface GeneralSettings {
  ioCoverRemovedEnable: boolean;
  ioCoverRemoved_videoBefore: number;
  ioCoverRemoved_videoAfter: number;
  ioCoverRemoved_media: string;
}

export interface VehicleTypeConfig {
  device: DeviceSettings;
  sensor: SensorSettings;
  aiEventDetection: AIEventDetectionSettings;
  speeding: SpeedingSettings;
  trafficAndSign: TrafficAndSignSettings;
  score: ScoreSettings;
  general: GeneralSettings;
}

export interface ConfigurationData {
  current: {
    passenger: VehicleTypeConfig;
    medium_vehicle: VehicleTypeConfig;
    heavy_duty: VehicleTypeConfig;
  };
  default: {
    passenger: VehicleTypeConfig;
    medium_vehicle: VehicleTypeConfig;
    heavy_duty: VehicleTypeConfig;
  };
}

export interface GetFullConfigurationResponse extends BaseResponse {
  data: ConfigurationData;
  doc_version: string;
  uq: string;
  build: string;
}

// Configuration interfaces for the UI components and API
export interface VehicleTypeInfo {
  id: number;
  name: string;
  code: string;
  events?: Array<{
    id: number;
    name: string;
    pre_event_length: number;
    post_event_length: number;
    videoOn: boolean;
    gpxOn: boolean;
  }>;
  video_format?: {
    c_mode: string;
    format: string;
  };
}

export interface ConfigurationResponse extends BaseResponse {
  data: {
    vehicle_types: VehicleTypeInfo[];
  };
}

// Fleet interfaces
export interface FleetConfigSet {
  configuration?: {
    event_settings?: {
      [key: string]: {
        videoOn?: boolean;
        pre_event_length?: number;
        post_event_length?: number;
      };
    };
    video_format?: {
      front?: string;
      incabin?: string;
      uvc?: string;
      tvi?: string;
    };
  };
}

export interface FleetConfigurationResponse extends BaseResponse {
  data: {
    configuration: {
      event_settings: {
        [key: string]: {
          videoOn: boolean;
          pre_event_length: number;
          post_event_length: number;
        };
      };
      video_format: {
        front: string;
        incabin: string;
        uvc: string;
        tvi: string;
      };
    };
  };
}

export interface UserAddFleetUserRequest {
  email: string;
  role: number;
  name: string;
}

export interface UserAddFleetUserResponse extends BaseResponse {
  data: {
    user_id: number;
  };
}

export enum UserListSortEnum {
  login = 'login',
  created = 'created',
  updated = 'updated',
  name = 'name'
}

export interface UserGetUserListResponse extends BaseResponse {
  data: {
    total_count: number;
    page_size: number;
    page: number;
    items: Array<{
      id: number;
      email: string;
      name: string;
      role: number;
      role_name: string;
      status: number;
      status_name: string;
      login_at: number;
      created_at: number;
      updated_at: number;
    }>;
  };
}

export interface UserEditFleetUserRequest {
  role: number;
  name: string;
}

export enum ContractFleetListSortEnum {
  updated = 'updated',
  created = 'created',
  device_number = 'device_number',
  name = 'name'
}

export interface FleetListResponse extends BaseResponse {
  data: {
    total_count: number;
    page_size: number;
    page: number;
    items: Array<{
      id: number;
      name: string;
      timezone_notification: string;
      device_number: number;
      account_number: number;
      created_at: number;
      updated_at: number;
    }>;
  };
}

export interface FleetGetFleetDetailResponse extends BaseResponse {
  data: {
    id: number;
    name: string;
    country_code: string;
    phone_number: string;
    address: string;
    notes: string;
    timezone_notification: string;
    logo_url: string;
    device_number: number;
    account_number: number;
    created_at: number;
    updated_at: number;
  };
}

export enum DeviceListSortEnum {
  serial_number = 'serial_number',
  activated_since = 'activated_since',
  app_version = 'app_version',
  name = 'name'
}

export interface DeviceGetDeviceListResponse extends BaseResponse {
  data: {
    total_count: number;
    page_size: number;
    page: number;
    items: Array<{
      id: number;
      cid: string;
      name: string;
      serial_number: string;
      imei1: string;
      imei2: string;
      app_version: string;
      firmware_version: string;
      base_image_version: string;
      region_image_version: string;
      ai_region: string;
      status: string;
      status_last_changed_at: number;
      activated_since: string;
      activated_from: string;
      first_connected_timestamp: number;
      install_position: string;
      front_camera_height: number;
      group_id: number;
      group_name: string;
      vehicle_type_id: number;
      vehicle_type_code: string;
      vehicle_type_name: string;
      license_plate: string;
      vin: string;
      model: string;
      adas_status: string;
      dms_status: string;
      plan_type: string;
      fleet_id: number;
      fleet_name: string;
    }>;
  };
}

export interface DeviceEditDeviceFleetRequest {
  device_ids: number[];
  fleet_id: number;
}

export interface DeviceGetAvailableModelsResponse extends BaseResponse {
  data: {
    items: string[];
  };
}

// Vehicle Type interfaces
export interface VehicleTypeReponse extends BaseResponse {
  data: {
    items: Array<{
      id: number;
      name: string;
      code: string;
    }>;
  };
}

// New Device Response Format
export interface NewDeviceResponse {
  count: number;
  data: NewDeviceInfo[];
  result: boolean;
  code: string;
  message: string;
  doc_version: string;
  uq: string;
  build: string;
}

// New Device Info Structure
export interface NewDeviceInfo {
  trip_info: any;
  image: string;
  pid: string;
  cid: string;
  organization_id: number;
  name: string;
  event_count: number;
  trip_count: number;
  latest_timestamp: string;
  first_connected_timestamp: number;
  last_connected_timestamp: number;
  is_online: boolean;
  connectivity_state: string;
  device_id: number;
  device_name: string;
  serial_number: string;
  latest_trips_id: number;
  latest_trips_status: string;
  latest_event_id: number;
  latest_event_type: string | null;
  latest_parking_timestamp: number;
  latest_lat: number;
  latest_lng: number;
  firmware_version: string | null;
  application_version: string;
  speedcam_version: string;
  last_update_time: string;
  group_id: number;
  group_name: string;
  license_plate: string | null;
  vehicle_identification_number: string | null;
  vehicle_type_id: number;
  plan_type: string;
  inserted_at: string;
  product_name: string;
  sku_name: string;
  speedcam_file_md5: string;
  speedcam_version_name: string | null;
  active_at: string;
  install_position: string;
  driver_position: string;
  last_device_server_connection_speed: string;
  mmf_detail_map_version: string;
  miai_service_version: string;
  latest_in_use_satellite_count: number;
  vehicle_id: number | null;
  version: {
    mmf_poi_map_versions: any;
    mmf_detail_map_versions: any;
    speed_camera_versions: Array<{
      region: string;
      version: string;
    }>;
    mmf_base_map_versions: any;
  };
  keep_alive_timestamp: number;
  IMEI_1: number;
  IMEI_2: number | null;
  region: string;
  active_since: number;
  camera_height: number;
  cover_status: string;
  custom_camera_height_changed: boolean;
  liveview_session: {
    activated: boolean;
    count: number;
    remaining_time: number;
  };
  snapshot: Array<{
    type: number;
    image: string;
  }>;
  total_photo: string | number;
  total_photo_msg: string;
  onging_live_trip: string;
}

export interface LiveViewResponse {
  data: {
    credentials: LiveViewCredentials;
    region: string;
    deviceGateway: string;
    cid: string;
    prefix: string;
    request_id: string;
    root: string;
    remaining_time: number;
    signaling_state: null;
    C_I: boolean;
    liveview_setting: {
      bit_rate: number;
      in_cabin: boolean;
      resolution: string;
      time_limit: number;
    };
    libs: {
      rtc: {
        [key: string]: {
          deprecated: boolean;
        };
      };
    };
  };
  result: boolean;
  code: string;
  message: string;
  doc_version: string;
  uq: string;
  build: string;
}

interface LiveViewCredentials {
  key: string;
  secret: string;
  token: string;
  expires: number;
  topics: {
    credential: string;
    device_report: string;
  };
  client_id: string[];
}
// New Dashboard Response
export interface NewDashboardResponse {
  data: {
    online: number;
    all_device: number;
    impact_count: {
      impact: number;
      parking_impact: number;
    };
    health_report: {
      count: number;
      preference: {
        signal_notice: number;
        signal_attention: number;
        storage_notice: number;
        storage_attention: number;
      };
      list: any[];
    };
    trip_summary: {
      driving_time_in_seconds: number;
      trips: number;
      idling_time_in_seconds: number;
      total_distance_in_metric: number;
      trips_by_unknown_driver: number;
    };
    statistics: {
      driver: {
        top: any | null;
        last: any | null;
      };
    };
  };
  result: boolean;
  code: string;
  message: string;
  doc_version: string;
  uq: string;
  build: string;
}


export interface NewDashboardResponse {
  data: {
    online: number;
    all_device: number;
    impact_count: {
      impact: number;
      parking_impact: number;
    };
    health_report: {
      count: number;
      preference: {
        signal_notice: number;
        signal_attention: number;
        storage_notice: number;
        storage_attention: number;
      };
      list: any[]; // You may want to define a more specific type based on actual data
    };
    trip_summary: {
      driving_time_in_seconds: number;
      trips: number;
      idling_time_in_seconds: number;
      total_distance_in_metric: number;
      trips_by_unknown_driver: number;
    };
    statistics: {
      driver: {
        top: any | null; // Define specific type if available
        last: any | null; // Define specific type if available
      };
    };
  };
  result: boolean;
  code: string;
  message: string;
  doc_version: string;
  uq: string;
  build: string;
}

// Add this new interface to visionMaxApi.ts
export interface  NewEventsResponse {
  count: number;
  data: Array<{
    device_cid: string;
    car_id: number | null;
    driver_id: number | null;
    type: number;
    type_name: string;
    lat: number;
    lng: number;
    tag: number;
    ticket: string;
    recording_id: number | null;
    timestamp: number;
    name: string;
    uuid: string;
    trip_id: number;
    is_new: boolean;
    clicked_count: number;
    driver_name: string | null;
    driver_avatar: string | null;
    driver_status: string | null;
    videos_count: number | null;
    videos_total_count: number | null;
    video_uploaded: number;
    ticket_linked: string | null;
    severity_level: number;
    organization_id: number;
    invalid_ic_card: string | null;
    headshots_event_id: number;
    expiry: number;
    expired: boolean;
    clock_in_status: {
      nfc_clock_in: boolean;
      headshot: boolean;
      recognized: boolean;
      driver_avatar: boolean;
      headshot_image_url: string;
      driver_avatar_url: boolean | string;
    };
    asset_info: {
      uploading: boolean;
      has_video?: boolean;
      has_snapshot?: boolean;
      notify_only?: boolean;
    };
    upload_status: number;
  }>;
  result: boolean;
  code: string;
  message: string;
  doc_version: string;
  uq: string;
  build: string;
}


// Dashboard interfaces
export interface DashboardResponse extends BaseResponse {
  data: {
    timezone: string;
    impact_events: {
      last_7_days: number;
      last_30_days: number;
    };
    trip_summary: {
      last_7_days: {
        trip_count: number;
        distance: number;
        idle_time: number;
        duration: number;
      };
      last_30_days: {
        trip_count: number;
        distance: number;
        idle_time: number;
        duration: number;
      };
    };
  };
}

export interface HighLightListResponse extends BaseResponse {
  data: {
    items: Array<{
      id: number;
      driver_id: number;
      driver_name: string;
      driver_avatar: string;
      driver_status: boolean;
      ticket: string;
      event_id: number;
      event_type: number;
      event_name: string;
      severity_level: string;
      time: number;
      asset_id: string;
      snapshot: string;
      expired: boolean;
      left_days: number;
      video_uploaded: boolean;
    }>;
    total: number;
  };
}

// Data Usage interfaces
export interface DataUsageResponse extends BaseResponse {
  data: {
    items: Array<{
      day: string;
      upload: number;
      download: number;
    }>;
  };
}

/**VisionMaxAPIVisionMaxAPIVisionMaxAPI
 * VisionMax API Client class
 */
export class VisionMaxAPI {
  private axiosInstance: AxiosInstance;
  private config: ApiConfig;

  constructor(config: ApiConfig) {
    this.config = config;
    this.axiosInstance = axios.create({
      baseURL: config.baseURL,
      headers: {
        'Content-Type': 'application/json',
        ...(config.apiKey ? { 'api-key': config.apiKey } : {"x-api-key": "WnbpXF1lJ1vKePo1C5pCXbgVGbkwehE8wMvX5LV9ikQ"}),
        ...(config.token ? { 'Authorization': `Bearer ${config.token}` } : {})
      }
    });

    // Add response interceptor to handle common errors
    this.axiosInstance.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response) {
          // Handle specific error codes
          if (error.response.status === 401) {
            console.error('Authentication error: Token may be expired');
          } else if (error.response.status === 403) {
            console.error('Authorization error: Insufficient permissions');
          }
        }
        return Promise.reject(error);
      }
    );
  }

  /**
   * Update the authorization token
   */
  public setToken(token: string): void {
    this.config.token = token;
    this.axiosInstance.defaults.headers.common['Authorization'] = `Bearer ${token}`;
  }

  /**
   * Authentication methods
   */
  public async authenticate(email: string, password: string): Promise<AuthResponse> {
    const response = await this.axiosInstance.post<AuthResponse>('/V2/auth', { email, password });
    if (response.data.status === 'success' && response.data.data.token) {
      this.setToken(response.data.data.token);
    }
    return response.data;
  }

  public async verifyToken(): Promise<BaseResponse> {
    const response = await this.axiosInstance.get<BaseResponse>('/V2/auth/token-verify');
    return response.data;
  }

  public async logout(): Promise<BaseResponse> {
    const response = await this.axiosInstance.get<BaseResponse>('/logout');
    this.config.token = undefined;
    // Remove the Authorization header properly
    delete this.axiosInstance.defaults.headers.common['Authorization'];
    return response.data;
  }

  public async switchFleet(fleetId: number): Promise<FleetTokenSwitchResponse> {
    const formData = new FormData();
    formData.append('fleet_id', fleetId.toString());
    
    const response = await this.axiosInstance.post<FleetTokenSwitchResponse>('/V2/auth/token-switch', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
    
    if (response.data.status === 'success' && response.data.data.token) {
      this.setToken(response.data.data.token);
    }
    
    return response.data;
  }

  public async initiateLiveView(cid: string, vid: string, mode: string = 'liveview'): Promise<LiveViewResponse> {
    const response = await this.axiosInstance.get<LiveViewResponse>('/V1/devices/initialLiveView', {
      params: {
        cid,
        vid,
        mode
      }
    });
    return response.data;
  }

  /**
   * Device methods
   */
  public async getDevices(params?: { 
    cid?: string, 
    order?: Order, 
    offset?: number, 
    limit?: number 
  }): Promise<NewDeviceResponse> {
    const response = await this.axiosInstance.get<NewDeviceResponse>('/V2/devices', { params });
    return response.data;
  }

  public async updateDevice(deviceData: DeviceUpdateRequest): Promise<BaseResponse> {
    const response = await this.axiosInstance.post<BaseResponse>('/V2/device/Update', deviceData);
    return response.data;
  }

  public async getTripReportFleetAvg(params: TripReportParams): Promise<TripReportFleetAvgResponse> {
    const queryParams = {
      since: params.since,
      until: params.until,
      tz: params.tz || 'Asia/Calcutta'
    };
  
    const response = await this.axiosInstance.get<TripReportFleetAvgResponse>(
      '/V2/analytics/GetFleetAvgPerDriverDay', 
      { params: queryParams }
    );
    return response.data;
  }
  
  public async getTripReportDriverSummary(params: TripReportParams): Promise<TripReportDriverSummaryResponse> {
    const queryParams = {
      since: params.since,
      until: params.until,
      sort: params.sort || 'number',
      order: params.order || Order.DESC,
      mode: params.mode || 'drivers_summary',
      tz: params.tz || 'Asia/Calcutta'
    };
  
    const response = await this.axiosInstance.get<TripReportDriverSummaryResponse>(
      '/V2/analytics/GetDriverTripSummaryList', 
      { params: queryParams }
    );
    return response.data;
  }
  
  
  public async getTripReportStatistics(params: TripReportParams): Promise<TripReportStatisticsResponse> {
    const queryParams = {
      since: params.since,
      until: params.until,
      mode: params.mode || 'all',
      tz: params.tz || 'Asia/Calcutta'
    };
  
    const response = await this.axiosInstance.get<TripReportStatisticsResponse>(
      '/V2/analytics/GetTripStatistics', 
      { params: queryParams }
    );
    return response.data;
  }

  public async getTrips(params: TripQueryParams = {}): Promise<TripsResponse> {
    const queryParams = {
      offset: params.offset || 0,
      limit: params.limit || 10,
      sort: params.sort || '',
      cid: params.cid || '',
      keyword: params.keyword || '',
      page: params.page || 1,
      pageSize: params.pageSize || 10,
      since: params.since,
      until: params.until,
      order: params.order || '',
      ...(params.status && { status: params.status })
    };
  
    const response = await this.axiosInstance.get<TripsResponse>('/V2/trips', { 
      params: queryParams 
    });
    return response.data;
  }

  public async getDeviceHealthReport(cid: string): Promise<HealthReportResponse> {
    const response = await this.axiosInstance.get<HealthReportResponse>(`/V2/devices/health/${cid}`);
    return response.data;
  }

  public async getDeviceSimpleHealthReport(cid: string): Promise<HealthSimpleReportResponse> {
    const response = await this.axiosInstance.get<HealthSimpleReportResponse>(`/V2/devices/healthReport/${cid}`);
    return response.data;
  }

  public async getDeviceStorageList(device: string): Promise<DeviceStorageListResponse> {
    const response = await this.axiosInstance.get<DeviceStorageListResponse>(`/V2/devices/${device}/storage-list`);
    return response.data;
  }

  public async formatSdCard(cid: string, request: FormatSdCardToDeviceRequest = {}): Promise<MqttCommandResponse> {
    const response = await this.axiosInstance.post<MqttCommandResponse>(`/V2/devices/FormatSdCardToDevice/${cid}`, request);
    return response.data;
  }

  public async deleteSDFile(cid: string, request: DeleteSDFileRequest): Promise<MqttCommandResponse> {
    const response = await this.axiosInstance.post<MqttCommandResponse>(`/V2/devices/DeleteSDFile/${cid}`, request);
    return response.data;
  }

  public async cancelTask(cid: string, request: CancelTaskRequest): Promise<MqttCommandResponse> {
    const response = await this.axiosInstance.post<MqttCommandResponse>(`/V2/devices/CancelTask/${cid}`, request);
    return response.data;
  }

  public async listTasks(cid: string, request: ListTaskRequest, page: number = 1, pageSize: number = 10): Promise<ListTaskResponse> {
    const response = await this.axiosInstance.post<ListTaskResponse>(
      `/V2/devices/ListTasks/${cid}`, 
      request,
      { params: { page, page_size: pageSize } }
    );
    return response.data;
  }

  /**
   * Trip methods
   */
  public async getTripStatistics(params: {
    since: number,
    until: number,
    mode: string,
    tz?: string
  }): Promise<TripStatisticsResponse> {
    const response = await this.axiosInstance.get<TripStatisticsResponse>('/V2/analytics/GetTripStatistics', { params });
    return response.data;
  }

  public async getFleetAvgPerDriverDay(params: {
    since: number,
    until: number,
    tz: string
  }): Promise<FleetAvgPerDriverDayResponse> {
    const response = await this.axiosInstance.get<FleetAvgPerDriverDayResponse>('/V2/analytics/GetFleetAvgPerDriverDay', { params });
    return response.data;
  }

  public async getDriverTripSummaryList(params: {
    since: number,
    until: number,
    mode: string,
    tz?: string
  }): Promise<DriverTripSummaryResponse> {
    const response = await this.axiosInstance.get<DriverTripSummaryResponse>('/V2/analytics/GetDriverTripSummaryList', { params });
    return response.data;
  }

  // public async getTrips(params: {
  //   sort?: string,
  //   status?: string[],
  //   keyword?: string,
  //   order?: Order,
  //   offset?: number,
  //   limit?: number,
  //   since: number,
  //   until: number
  // }): Promise<TripsResponse> {
  //   const response = await this.axiosInstance.get<TripsResponse>('/V2/trips', { params });
  //   return response.data;
  // }

  public async getTripDetail(tripId: number): Promise<TripDetailResponse> {
    const response = await this.axiosInstance.get<TripDetailResponse>('/V2/trip', { params: { trip_id: tripId } });
    return response.data;
  }

  /**
   * Event methods
   */
  public async getEvents(params: {
    sort?: string,
    keyword?: string,
    type?: string,
    order?: Order,
    offset?: number,
    limit?: number,
    since: number,
    until: number
  }): Promise<NewEventsResponse> {
    const response = await this.axiosInstance.get<NewEventsResponse>('/V2/events', { params });
    return response.data;
  }

  public async getEventDetail(ticket: string): Promise<EventDetailInfosResponse> {
    const response = await this.axiosInstance.get<EventDetailInfosResponse>('/V2/event/detailV2', { params: { ticket } });
    return response.data;
  }

  public async getEventVideoDetail(ticket: string): Promise<VideoDetailInfoResponse> {
    const formData = new FormData();
    formData.append('ticket', ticket);
    
    const response = await this.axiosInstance.post<VideoDetailInfoResponse>('/V2/event/GetVideoDetail', formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    });
    return response.data;
  }

  public async getStorageList(params: {
    sort?: string,
    keyword?: string,
    type?: string,
    order?: Order,
    offset?: number,
    limit?: number,
    since: number,
    until: number
  }): Promise<StorageListResponse> {
    const response = await this.axiosInstance.get<StorageListResponse>('/V2/event/StorageList', { params });
    return response.data;
  }

  public async requestVideoClip(sn: string, request: GetVideoClipRequest): Promise<GetVideoClipResponse> {
    const response = await this.axiosInstance.post<GetVideoClipResponse>(`/V2/event/videoClip/${sn}`, request);
    return response.data;
  }

  public async getTaskProcessResult(taskId: string): Promise<TaskProcessResultResponse> {
    const response = await this.axiosInstance.get<TaskProcessResultResponse>(`/V2/event/taskResult/${taskId}`);
    return response.data;
  }

  public async discardEvent(ticket: string): Promise<BaseResponse> {
    const formData = new FormData();
    formData.append('ticket', ticket);
    
    const response = await this.axiosInstance.post<BaseResponse>('/V2/event/discard', formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    });
    return response.data;
  }

  /**
   * Vehicle methods
   */
  public async getVehicleList(params?: {
    keyword?: string,
    status?: number[],
    page?: number,
    page_size?: number
  }): Promise<VehicleListResponse> {
    const response = await this.axiosInstance.get<VehicleListResponse>('/V2/vehicle/list', { params });
    return response.data;
  }

  public async getVehicleById(vehicleId: number): Promise<VehiclesDetail> {
    const response = await this.axiosInstance.get<VehiclesDetail>(`/V2/vehicle/${vehicleId}`);
    return response.data;
  }

  public async getVehicleByDeviceId(deviceId: number): Promise<VehiclesDetail> {
    const response = await this.axiosInstance.get<VehiclesDetail>(`/V2/vehicle/device/${deviceId}`);
    return response.data;
  }

  public async addVehicle(request: AddVehicleRequest): Promise<BaseResponse> {
    const response = await this.axiosInstance.post<BaseResponse>('/V2/vehicle/add', request);
    return response.data;
  }

  public async editVehicle(vehicleId: number, request: AddVehicleRequest): Promise<BaseResponse> {
    const response = await this.axiosInstance.post<BaseResponse>(`/V2/vehicle/${vehicleId}/edit`, request);
    return response.data;
  }

  public async deactivateVehicle(vehicleId: number): Promise<BaseResponse> {
    const response = await this.axiosInstance.post<BaseResponse>(`/V2/vehicle/${vehicleId}/deactivate`);
    return response.data;
  }

  public async activateVehicle(vehicleId: number): Promise<BaseResponse> {
    const response = await this.axiosInstance.post<BaseResponse>(`/V2/vehicle/${vehicleId}/activate`);
    return response.data;
  }

  public async deleteVehicle(vehicleId: number): Promise<BaseResponse> {
    const response = await this.axiosInstance.post<BaseResponse>(`/V2/vehicle/${vehicleId}/delete`);
    return response.data;
  }

  public async getAvailableDevices(): Promise<AvailableDevicesResponse> {
    const response = await this.axiosInstance.get<AvailableDevicesResponse>('/V2/vehicle/get-available-devices');
    return response.data;
  }
  public async checkVehicleAttributes(request: CheckVehicleAttributesRequest): Promise<CheckVehicleAttributesResponse> {
    const response = await this.axiosInstance.post<CheckVehicleAttributesResponse>('/V2/vehicle/check-vehicles-attributes', request);
    return response.data;
  }

  public async setVehicleOfDevice(request: SetVehicleOfDeviceRequest): Promise<BaseResponse> {
    const response = await this.axiosInstance.post<BaseResponse>('/V2/vehicle/set-vehicle-of-device', request);
    return response.data;
  }

  /**
   * ODB2 methods
   */
  public async getDeviceOdb2(serialNumber: string, params?: {
    since?: number,
    until?: number,
    timestamp?: number,
    pageSize?: number
  }): Promise<GetDeviceOdb2Response> {
    const response = await this.axiosInstance.get<GetDeviceOdb2Response>(`/V2/devices/odb2/${serialNumber}`, { params });
    return response.data;
  }

  /**
   * Setup methods
   */
  public async requestReCalibrate(serialNumber: string): Promise<SetUpBaseResponse> {
    const response = await this.axiosInstance.post<SetUpBaseResponse>(`/V2/device/${serialNumber}/setup/re-calibrate`);
    return response.data;
  }

  /**
   * Group methods
   */
  public async getGroupList(): Promise<GroupResponse> {
    const response = await this.axiosInstance.get<GroupResponse>('/V2/group/List');
    return response.data;
  }

  public async getAllGroupDevices(params?: {
    keyword?: string,
    status?: string,
    vehicle_type_id?: string,
    plan_type?: string,
    adas_function?: string,
    dms_function?: string,
    groups?: number
  }): Promise<ListGroupResponse> {
    const response = await this.axiosInstance.get<ListGroupResponse>('/V2/group/ListGroup', { params });
    return response.data;
  }

  public async getGroupDevices(groupId: number): Promise<ListGroupResponse> {
    const response = await this.axiosInstance.get<ListGroupResponse>(`/V2/group/ListGroup/${groupId}`);
    return response.data;
  }

  /**
   * Vehicle Type methods
   */
  public async getVehicleTypes(): Promise<VehicleTypeReponse> {
    const response = await this.axiosInstance.get<VehicleTypeReponse>('/V2/event/vehicleTypes');
    return response.data;
  }

  /**
   * Driver methods
   */
  public async getAllDrivers(params?: {
    keyword?: string,
    order?: Order,
    offset?: number,
    limit?: number
  }): Promise<NewDriverResponse> {
    const response = await this.axiosInstance.get<NewDriverResponse>('/V2/driver/GetAllDrivers', { params });
    return response.data;
  }

  /**
   * Configuration methods
   */
  public async getConfiguration(): Promise<ConfigurationResponse> {
    const response = await this.axiosInstance.get<ConfigurationResponse>('/V2/configuration');
    return response.data;
  }

  public async getFullConfiguration(): Promise<GetFullConfigurationResponse> {
    const response = await this.axiosInstance.get<GetFullConfigurationResponse>('/V2/configuration');
    return response.data;
  }

  public async updateConfiguration(payload: any): Promise<BaseResponse> {
    const response = await this.axiosInstance.post<BaseResponse>('/V2/configuration', payload);
    return response.data;
  }

  
  public async getDeviceConfiguration(cid: string): Promise<DeviceConfigurationResponse> {
    const response = await this.axiosInstance.get<DeviceConfigurationResponse>(`/V2/configuration/${cid}`);
    return response.data;
  }

  public async updateDeviceConfiguration(cid: string, request: ConfigSet): Promise<BaseResponse> {
    const response = await this.axiosInstance.post<BaseResponse>(`/V2/configuration/${cid}`, request);
    return response.data;
  }

  public async patchDeviceConfiguration(cid: string, request: ConfigSet): Promise<BaseResponse> {
    const response = await this.axiosInstance.patch<BaseResponse>(`/V2/configuration/${cid}`, request);
    return response.data;
  }

  public async restoreDeviceConfiguration(cid: string): Promise<BaseResponse> {
    const response = await this.axiosInstance.post<BaseResponse>(`/V2/configuration/${cid}/restore`);
    return response.data;
  }

  /**
   * Fleet Configuration methods
   */
  public async getFleetConfiguration(fleetId: string): Promise<FleetConfigurationResponse> {
    const response = await this.axiosInstance.get<FleetConfigurationResponse>(`/V2/fleet/${fleetId}/configurations`);
    return response.data;
  }

  public async updateFleetConfiguration(fleetId: string, request: FleetConfigSet): Promise<BaseResponse> {
    const response = await this.axiosInstance.post<BaseResponse>(`/V2/fleet/${fleetId}/configurations`, request);
    return response.data;
  }

  /**
   * Fleet User management methods
   */
  public async addFleetUser(request: UserAddFleetUserRequest): Promise<UserAddFleetUserResponse> {
    const response = await this.axiosInstance.post<UserAddFleetUserResponse>('/V2/fleet/user/add', request);
    return response.data;
  }

  public async getAvailableGroups(): Promise<AvailableGroupsResponse> {
    const response = await this.axiosInstance.get<AvailableGroupsResponse>('/V2/vehicle/get-available-groups');
    return response.data;
  }

  public async getFleetUserList(params?: {
    keyword?: string,
    role?: number[],
    status?: number[],
    sort?: UserListSortEnum,
    order?: Order,
    page?: number,
    page_size?: number
  }): Promise<UserGetUserListResponse> {
    const response = await this.axiosInstance.get<UserGetUserListResponse>('/V2/fleet/user/list', { params });
    return response.data;
  }

  public async deactivateFleetUser(userId: string): Promise<BaseResponse> {
    const response = await this.axiosInstance.post<BaseResponse>(`/V2/fleet/user/${userId}/deactivate`);
    return response.data;
  }

  public async reactivateFleetUser(userId: string): Promise<BaseResponse> {
    const response = await this.axiosInstance.post<BaseResponse>(`/V2/fleet/user/${userId}/reactivate`);
    return response.data;
  }

  public async deleteFleetUser(userId: string): Promise<BaseResponse> {
    const response = await this.axiosInstance.post<BaseResponse>(`/V2/fleet/user/${userId}/delete`);
    return response.data;
  }

  public async editFleetUser(userId: string, request: UserEditFleetUserRequest): Promise<BaseResponse> {
    const response = await this.axiosInstance.post<BaseResponse>(`/V2/fleet/user/${userId}/edit`, request);
    return response.data;
  }

  /**
   * Contract Fleet methods
   */
  public async addContractFleet(data: FormData): Promise<BaseResponse> {
    const response = await this.axiosInstance.post<BaseResponse>('/V2/fleet/add', data, {
      headers: { 'Content-Type': 'multipart/form-data' }
    });
    return response.data;
  }

  public async getContractFleetList(params?: {
    keyword?: string,
    sort?: ContractFleetListSortEnum,
    order?: Order,
    page?: number,
    page_size?: number
  }): Promise<FleetListResponse> {
    const response = await this.axiosInstance.get<FleetListResponse>('/V2/fleet/list', { params });
    return response.data;
  }

  public async getFleetDetail(fleetId: string): Promise<FleetGetFleetDetailResponse> {
    const response = await this.axiosInstance.get<FleetGetFleetDetailResponse>(`/V2/fleet/${fleetId}`);
    return response.data;
  }

  public async editFleet(fleetId: string, data: FormData): Promise<BaseResponse> {
    const response = await this.axiosInstance.post<BaseResponse>(`/V2/fleet/${fleetId}/edit`, data, {
      headers: { 'Content-Type': 'multipart/form-data' }
    });
    return response.data;
  }

  public async deleteContractFleet(fleetId: string): Promise<BaseResponse> {
    const response = await this.axiosInstance.post<BaseResponse>(`/V2/fleet/${fleetId}/delete`);
    return response.data;
  }

  /**
   * Device-Fleet methods
   */
  public async getDeviceList(params?: {
    keyword?: string,
    model?: string[],
    fleet_ids?: number[],
    sort?: DeviceListSortEnum,
    order?: Order,
    page?: number,
    page_size?: number
  }): Promise<DeviceGetDeviceListResponse> {
    const response = await this.axiosInstance.get<DeviceGetDeviceListResponse>('/V2/fleet/device/list', { params });
    return response.data;
  }

  public async editDeviceFleet(request: DeviceEditDeviceFleetRequest): Promise<BaseResponse> {
    const response = await this.axiosInstance.post<BaseResponse>('/V2/fleet/device/edit-fleet', request);
    return response.data;
  }

  public async getAvailableModels(): Promise<DeviceGetAvailableModelsResponse> {
    const response = await this.axiosInstance.get<DeviceGetAvailableModelsResponse>('/V2/fleet/device/available-models');
    return response.data;
  }

  /**
   * Dashboard methods
   */
  public async getDashboard(tz?: string): Promise<NewDashboardResponse> {
    const response = await this.axiosInstance.get<NewDashboardResponse>('/V2/dashboard', { params: { tz } });
    return response.data;
  }

  public async getHighlightList(params?: {
    order?: Order,
    offset?: number,
    limit?: number
  }): Promise<HighLightListResponse> {
    const response = await this.axiosInstance.get<HighLightListResponse>('/V2/dashboard/highlight-list', { params });
    return response.data;
  }

  /**
   * Data Usage methods
   */
  public async getDataUsage(params: {
    sN: string,
    from: string,
    to: string,
    frequency: string,
    separate?: boolean
  }): Promise<DataUsageResponse> {
    const response = await this.axiosInstance.get<DataUsageResponse>('/V2/ota/data-usage', { params });
    return response.data;
  }

  /**
   * Webhook methods
   */
  public async listWebhooks(): Promise<ListWebhookResponse> {
    const response = await this.axiosInstance.get<ListWebhookResponse>('/webhook');
    return response.data;
  }

  public async subscribeWebhook(request: WebhookSubscribeRequest): Promise<VmxBaseResponse> {
    const response = await this.axiosInstance.post<VmxBaseResponse>('/webhook/subscribe', request);
    return response.data;
  }

  public async unsubscribeWebhook(request: WebhookUnscribeRequest): Promise<VmxBaseResponse> {
    const response = await this.axiosInstance.post<VmxBaseResponse>('/webhook/unsubscribe', request);
    return response.data;
  }

  public async getWebhookPublicKey(kid: string): Promise<string> {
    const response = await this.axiosInstance.get<string>(`/webhook-signing/${kid}`, {
      responseType: 'text'
    });
    return response.data;
  }

  public async createDriver(driverData: CreateDriverRequest, sessionId?: string): Promise<CreateDriverResponse> {
    const requestData: any = {
      name: driverData.name,
      employee_id: driverData.employee_id,
      rfid: driverData.rfid,
      email: driverData.email,
      phone: driverData.phone
    };
  

  
    // Handle logo based on its type
    if (driverData.logo) {
      if (driverData.logo instanceof File) {
        // Convert File to base64 string
        const base64Logo = await this.fileToBase64(driverData.logo);
        requestData.logo = base64Logo;
      } else if (typeof driverData.logo === 'string') {
        // Logo is already a base64 string or URL
        requestData.logo = driverData.logo;
      }
    } else {
      requestData.logo = null;
    }

    const requestBody = {
      driver: {
        name: driverData.name,
        employee_id: driverData.employee_id,
        rfid: driverData.rfid,
        email: driverData.email,
        phone: driverData.phone,
        logo: requestData.logo
      },
      state: "finished" // or you might want to set this as a parameter
    };
  
    // If sessionId is provided, use the session-based endpoint
    const endpoint = sessionId 
      ? `/V2/driver/SaveNewDriverSessionData/${sessionId}`
      : '/V2/driver/create';
  
    const response = await this.axiosInstance.post<CreateDriverResponse>(endpoint, requestBody, {
      headers: {
        'Content-Type': 'application/json'
      }
    });
    return response.data;
  }

  // Helper method to convert File to base64
private async fileToBase64(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => {
      if (typeof reader.result === 'string') {
        resolve(reader.result);
      } else {
        reject(new Error('Failed to convert file to base64'));
      }
    };
    reader.onerror = error => reject(error);
  });
}
  
  /**
   * Start a new driver creation session (if needed)
   * This method can be used to get a session ID before creating a driver
   */
  public async startDriverSession(): Promise<{ data: any }> {
    const response = await this.axiosInstance.get('V2/driver/StartNewDriverSession');
    return response.data;
    
  }
  
  /**
   * Alternative method if your API expects JSON instead of FormData
   */
  public async createDriverJson(driverData: {
    name: string;
    employee_id: string;
    rfid: string;
    email: string;
    phone: string;
    logo?: string; // base64 encoded image
  }): Promise<CreateDriverResponse> {
    const response = await this.axiosInstance.post<CreateDriverResponse>('/V2/driver/create', driverData);
    return response.data;
  }
  
  /**
   * Update an existing driver
   */
  public async updateDriver(driverId: number, driverData: Partial<CreateDriverRequest>): Promise<CreateDriverResponse> {
    const formData = new FormData();
    
    if (driverData.name) formData.append('name', driverData.name);
    if (driverData.employee_id) formData.append('employeeId', driverData.employee_id);
    if (driverData.rfid) formData.append('rfid', driverData.rfid);
    if (driverData.email) formData.append('email', driverData.email);
    if (driverData.phone) formData.append('phone', driverData.phone);
    if (driverData.logo) formData.append('logo', driverData.logo);
  
    const response = await this.axiosInstance.post<CreateDriverResponse>(`/V2/driver/${driverId}/update`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
    return response.data;
  }
  
  /**
   * Delete a driver
   */
  public async deleteDriver(driverId: number): Promise<BaseResponse> {
    const response = await this.axiosInstance.get<BaseResponse>(`/V2/driver/DeleteDriver/${driverId}?driver_id=${driverId}`);
    return response.data;
  }
  
  /**
   * Get driver by ID
   */
  public async getDriverById(driverId: number): Promise<CreateDriverResponse> {
    const response = await this.axiosInstance.get<CreateDriverResponse>(`/V2/driver/${driverId}`);
    return response.data;
  }
  
  /**
   * Activate a driver
   */
  public async activateDriver(driverId: number): Promise<BaseResponse> {
    const response = await this.axiosInstance.post<BaseResponse>(`/V2/driver/${driverId}/activate`);
    return response.data;
  }
  
  /**
   * Deactivate a driver
   */
  public async deactivateDriver(driverId: number,driverName:string): Promise<BaseResponse> {
    const response = await this.axiosInstance.get<BaseResponse>(`/V2/driver/DisableDriver/${driverId}?driver_id=${driverId}&name=${driverName}`);
    return response.data;
  }
}

/**
 * Usage example
 */
/*
// Initialize the API with your configuration
const api = new VisionMaxAPI({
  baseURL: 'https://staging-api.visionmaxfleet.com',
  apiKey: 'your-api-key'
});

// Authentication
async function login() {
  try {
    const authResult = await api.authenticate('<EMAIL>', 'your-password');
    console.log('Logged in successfully:', authResult.data.user_info);
  } catch (error) {
    console.error('Login failed:', error);
  }
}

// Get devices
async function getDevices() {
  try {
    const devices = await api.getDevices({ limit: 10 });
    console.log(`Found ${devices.data.total} devices:`, devices.data.items);
  } catch (error) {
    console.error('Failed to get devices:', error);
  }
}

// Get events
async function getEvents() {
  const oneWeekAgo = Math.floor(Date.now() / 1000) - (7 * 24 * 60 * 60);
  const now = Math.floor(Date.now() / 1000);
  
  try {
    const events = await api.getEvents({
      since: oneWeekAgo,
      until: now,
      limit: 10
    });
    console.log(`Found ${events.data.total} events:`, events.data.events);
  } catch (error) {
    console.error('Failed to get events:', error);
  }
}
*/