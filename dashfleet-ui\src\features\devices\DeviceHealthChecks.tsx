// import React, { useState, useEffect } from 'react';
// import { <PERSON>, Check, AlertTriangle, X, RefreshCw } from 'lucide-react';

// import { Card, CardContent, CardHeader, CardTitle } from '../../components/common/Card';
// import { Button } from '../../components/common/Button';
// import { Spinner } from '../../components/common/Spinner';
// import { Alert } from '../../components/common/Alert';

// import { VisionMaxAPI, HealthSimpleReportResponse, DeviceInfo } from '../../api/visionMaxApi';

// interface DeviceHealthChecksProps {
//   deviceId: string;
//   health: string;
// }

// interface HealthCheck {
//   status: string;
//   name: string;
//   description: string;
//   actionRequired?: boolean;
//   details?: string;
// }

// interface MaintenanceInfo {
//   lastMaintenance?: string;
//   nextScheduled?: string;
//   history?: {
//     date: string;
//     type: string;
//     technician: string;
//   }[];
// }

// interface FirmwareInfo {
//   currentVersion?: string;
//   latestVersion?: string;
//   lastUpdated?: string;
//   updateAvailable?: boolean;
// }

// interface SoftwareInfo {
//   appVersion?: string;
//   latestVersion?: string;
//   lastUpdated?: string;
//   updateAvailable?: boolean;
// }

// interface ConfigurationInfo {
//   version?: string;
//   lastModified?: string;
//   status?: string;
// }

// interface HealthCheckData {
//   lastRun?: string;
//   checks: HealthCheck[];
//   maintenance?: MaintenanceInfo;
//   firmware?: FirmwareInfo;
//   software?: SoftwareInfo;
//   configuration?: ConfigurationInfo;
// }

// export const DeviceHealthChecks: React.FC<DeviceHealthChecksProps> = ({ 
//   deviceId,
//   health,
// }) => {
//   const [healthChecks, setHealthChecks] = useState<HealthCheckData | null>(null);
//   const [loading, setLoading] = useState(true);
//   const [error, setError] = useState<Error | null>(null);
  
//   // Create API instance
//   const token = localStorage.getItem('token');
//   const api = new VisionMaxAPI({
//     baseURL: process.env.REACT_APP_API_BASE_URL || 'https://api.visionmaxfleet.com',
//     token
//   });
  
//   // Fetch health report data
//   const fetchHealthReport = async () => {
//     setLoading(true);
//     setError(null);
    
//     try {
//       // Get device health report
//       const response = await api.getDeviceSimpleHealthReport(deviceId);
      
//       // Get device details to extract version info
//       const deviceResponse = await api.getDevices({ cid: deviceId, limit: 1 });
//       const device = deviceResponse.data.items[0];
      
//       // Transform API response to our component's expected format
//       const transformedData: HealthCheckData = {
//         lastRun: new Date().toLocaleString(),
//         checks: response.data.reports.map(report => ({
//           status: report.status.toLowerCase(),
//           name: report.title,
//           description: report.description,
//           actionRequired: report.priority > 1,
//           details: report.suggestion
//         })),
//         maintenance: {
//           lastMaintenance: 'Not available',
//           nextScheduled: 'Not scheduled',
//           history: []
//         },
//         firmware: {
//           currentVersion: device.firmwareVersion,
//           latestVersion: device.firmwareVersion,
//           lastUpdated: device.activatedSince,
//           updateAvailable: false
//         },
//         software: {
//           appVersion: device.appVersion,
//           latestVersion: device.appVersion,
//           lastUpdated: device.activatedSince,
//           updateAvailable: false
//         },
//         configuration: {
//           version: '1.0',
//           lastModified: device.activatedSince,
//           status: 'Active'
//         }
//       };
      
//       setHealthChecks(transformedData);
//     } catch (err) {
//       setError(err instanceof Error ? err : new Error('Failed to fetch health report'));
//     } finally {
//       setLoading(false);
//     }
//   };
  
//   // Load health report on mount
//   useEffect(() => {
//     fetchHealthReport();
//   }, [deviceId]);
  
//   // Handle run diagnostics
//   const handleRunDiagnostics = () => {
//     fetchHealthReport();
//   };
  
//   // Get status indicator
//   const getStatusIndicator = (status: string) => {
//     switch (status) {
//       case 'pass':
//         return <Check size={18} className="text-green-500" />;
//       case 'warning':
//         return <AlertTriangle size={18} className="text-yellow-500" />;
//       case 'fail':
//         return <X size={18} className="text-red-500" />;
//       default:
//         return <RefreshCw size={18} className="text-gray-500" />;
//     }
//   };
  
//   // Get status class
//   const getStatusClass = (status: string) => {
//     switch (status) {
//       case 'pass':
//         return 'bg-green-50 border-green-200';
//       case 'warning':
//         return 'bg-yellow-50 border-yellow-200';
//       case 'fail':
//         return 'bg-red-50 border-red-200';
//       default:
//         return 'bg-gray-50 border-gray-200';
//     }
//   };
  
//   // Loading state
//   if (loading) {
//     return (
//       <div className="flex justify-center items-center p-8">
//         <Spinner size="large" color="primary" />
//       </div>
//     );
//   }
  
//   // Error state
//   if (error) {
//     return (
//       <Alert
//         type="error"
//         title="Error Loading Health Checks"
//         message="There was a problem loading the device health checks. Please try again."
//         action={{
//           label: 'Retry',
//           onClick: fetchHealthReport
//         }}
//       />
//     );
//   }
  
//   const overallColor = 
//     health === 'Healthy' ? 'text-green-600' : 
//     health === 'Warning' ? 'text-yellow-600' : 
//     'text-red-600';
  
//   return (
//     <div className="space-y-6">
//       <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
//         <Card className="md:col-span-2">
//           <CardHeader>
//             <CardTitle>Health Status</CardTitle>
//           </CardHeader>
//           <CardContent>
//             <div className="flex items-center mb-6">
//               <div className={`text-4xl font-bold ${overallColor}`}>
//                 {health}
//               </div>
//               <Button 
//                 variant="outline" 
//                 className="ml-auto"
//                 onClick={handleRunDiagnostics}
//               >
//                 <RefreshCw size={16} className="mr-2" />
//                 Run Diagnostics
//               </Button>
//             </div>
            
//             {healthChecks?.lastRun && (
//               <div className="text-sm text-gray-500 mb-4">
//                 Last diagnostics run: {healthChecks.lastRun}
//               </div>
//             )}
            
//             <div className="space-y-4">
//               {healthChecks?.checks.map((check, index) => (
//                 <div
//                   key={index}
//                   className={`p-4 border rounded-lg ${getStatusClass(check.status)}`}
//                 >
//                   <div className="flex items-center justify-between">
//                     <div className="flex items-center">
//                       {getStatusIndicator(check.status)}
//                       <div className="ml-3">
//                         <div className="font-medium">{check.name}</div>
//                         <div className="text-sm text-gray-600">{check.description}</div>
//                       </div>
//                     </div>
//                     {check.actionRequired && (
//                       <Button 
//                         variant="outline"
//                         size="sm"
//                       >
//                         Fix Issue
//                       </Button>
//                     )}
//                   </div>
//                   {check.details && (
//                     <div className="mt-2 text-sm text-gray-600 pl-8">
//                       {check.details}
//                     </div>
//                   )}
//                 </div>
//               ))}
//             </div>
//           </CardContent>
//         </Card>
        
//         <Card>
//           <CardHeader>
//             <CardTitle>Maintenance</CardTitle>
//           </CardHeader>
//           <CardContent>
//             <div className="space-y-4">
//               <div>
//                 <div className="text-sm text-gray-500">Last Maintenance</div>
//                 <div className="text-lg font-medium">
//                   {healthChecks?.maintenance?.lastMaintenance || 'Never'}
//                 </div>
//               </div>
              
//               <div>
//                 <div className="text-sm text-gray-500">Next Scheduled</div>
//                 <div className="text-lg font-medium">
//                   {healthChecks?.maintenance?.nextScheduled || 'Not scheduled'}
//                 </div>
//               </div>
              
//               <div className="pt-4">
//                 <div className="text-sm font-medium mb-2">Maintenance History</div>
//                 {healthChecks?.maintenance?.history && healthChecks.maintenance.history.length > 0 ? (
//                   <div className="space-y-2">
//                     {healthChecks.maintenance.history.map((item, index) => (
//                       <div key={index} className="text-sm border-l-2 border-blue-300 pl-3 py-1">
//                         <div className="font-medium">{item.date}</div>
//                         <div className="text-gray-600">{item.type}</div>
//                         <div className="text-xs text-gray-500">{item.technician}</div>
//                       </div>
//                     ))}
//                   </div>
//                 ) : (
//                   <div className="text-sm text-gray-500">
//                     No maintenance history available
//                   </div>
//                 )}
//               </div>
              
//               <div className="pt-4">
//                 <Button className="w-full">
//                   <Shield size={16} className="mr-2" />
//                   Schedule Maintenance
//                 </Button>
//               </div>
//             </div>
//           </CardContent>
//         </Card>
//       </div>
      
//       {/* Firmware Information */}
//       <Card>
//         <CardHeader>
//           <CardTitle>Firmware & Software</CardTitle>
//         </CardHeader>
//         <CardContent>
//           <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
//             <div>
//               <h3 className="text-sm font-medium mb-2">Firmware</h3>
//               <div className="space-y-2">
//                 <div className="flex justify-between">
//                   <span className="text-sm text-gray-500">Current Version</span>
//                   <span className="text-sm font-medium">{healthChecks?.firmware?.currentVersion}</span>
//                 </div>
//                 <div className="flex justify-between">
//                   <span className="text-sm text-gray-500">Latest Version</span>
//                   <span className="text-sm font-medium">{healthChecks?.firmware?.latestVersion}</span>
//                 </div>
//                 <div className="flex justify-between">
//                   <span className="text-sm text-gray-500">Last Updated</span>
//                   <span className="text-sm font-medium">{healthChecks?.firmware?.lastUpdated}</span>
//                 </div>
//               </div>
//               {healthChecks?.firmware?.updateAvailable && (
//                 <Button className="mt-4 w-full">
//                   Update Firmware
//                 </Button>
//               )}
//             </div>
            
//             <div>
//               <h3 className="text-sm font-medium mb-2">Software</h3>
//               <div className="space-y-2">
//                 <div className="flex justify-between">
//                   <span className="text-sm text-gray-500">App Version</span>
//                   <span className="text-sm font-medium">{healthChecks?.software?.appVersion}</span>
//                 </div>
//                 <div className="flex justify-between">
//                   <span className="text-sm text-gray-500">Latest Version</span>
//                   <span className="text-sm font-medium">{healthChecks?.software?.latestVersion}</span>
//                 </div>
//                 <div className="flex justify-between">
//                   <span className="text-sm text-gray-500">Last Updated</span>
//                   <span className="text-sm font-medium">{healthChecks?.software?.lastUpdated}</span>
//                 </div>
//               </div>
//               {healthChecks?.software?.updateAvailable && (
//                 <Button className="mt-4 w-full">
//                   Update Software
//                 </Button>
//               )}
//             </div>
            
//             <div>
//               <h3 className="text-sm font-medium mb-2">Configuration</h3>
//               <div className="space-y-2">
//                 <div className="flex justify-between">
//                   <span className="text-sm text-gray-500">Config Version</span>
//                   <span className="text-sm font-medium">{healthChecks?.configuration?.version}</span>
//                 </div>
//                 <div className="flex justify-between">
//                   <span className="text-sm text-gray-500">Last Modified</span>
//                   <span className="text-sm font-medium">{healthChecks?.configuration?.lastModified}</span>
//                 </div>
//                 <div className="flex justify-between">
//                   <span className="text-sm text-gray-500">Status</span>
//                   <span className="text-sm font-medium">{healthChecks?.configuration?.status}</span>
//                 </div>
//               </div>
//               <Button className="mt-4 w-full" variant="outline">
//                 View Configuration
//               </Button>
//             </div>
//           </div>
//         </CardContent>
//       </Card>
//     </div>
//   );
// };

// export default DeviceHealthChecks;