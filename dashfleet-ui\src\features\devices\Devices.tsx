import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { format, fromUnixTime, subDays } from 'date-fns';
import { Filter, Download, Plus, Search, Truck, RefreshCw } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/common/Card';
import { Button } from '../../components/common/Button';
import { DataFilter } from '../../components/common/DataFilter';
import { DevicesTable } from './DevicesTable';
import { Spinner } from '../../components/common/Spinner';
import { Alert } from '../../components/common/Alert';
import { Pagination } from '../../components/common/Pagination';
import { DeviceStatusChart } from '../../components/charts/DeviceStatusChart';

import { VisionMaxAPI, DeviceInfo, NewDeviceInfo } from '../../api/visionMaxApi';

interface DeviceData {
  devices: NewDeviceInfo[];
  totalCount: number;
  stats: {
    online: number;
    offline: number;
    maintenance: number;
  };
}

const Devices: React.FC = () => {
  const navigate = useNavigate();

  // Search state
  const [searchTerm, setSearchTerm] = useState('');

  // Filter state
  const [dateRange, setDateRange] = useState({
    startDate: subDays(new Date(), 30), // Last 30 days
    endDate: new Date(),
  });

  const [filters, setFilters] = useState({
    status: [] as string[],
    model: [] as string[],
    lastSeen: [] as string[],
  });

  // Pagination
  const [page, setPage] = useState(1);
  const [perPage, setPerPage] = useState(10);

  // Devices data
  const [data, setData] = useState<DeviceData | null>(null);
  const [loading, setLoading] = useState(true);
  const [fetching, setFetching] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  // Create API instance
  const token = localStorage.getItem('authToken');
  const api = new VisionMaxAPI({
    baseURL: import.meta.env.VITE_APP_API_BASE_URL?.replace('/V2', '') || 'https://api.visionmaxfleet.com',
    token
  });

  // Add this interface to match what DevicesTable expects
  interface DeviceTableItem {
    id: string;
    name: string;
    serialNumber: string;
    model: string;
    status: string;
    lastSeen?: string;
    adasHealth: string;
    planType: string; // Added this required field
    activatedSince: string;
    appVersion: string;
    licensePlate?: string; // May be needed
    vin?: string; // May be needed
    vehicleTypeName?: string; // May be needed
    region: string;
    satellites: number;
    camera_height: number;
    cid: string;

  }

  // Create a more complete adapter function
  const adaptDeviceInfo = (deviceInfo: NewDeviceInfo): DeviceTableItem => {
    return {
      id: deviceInfo.device_id.toString(),
      name: deviceInfo.name,
      serialNumber: deviceInfo.serial_number,
      model: deviceInfo.sku_name || '',
      status: deviceInfo.connectivity_state,
      lastSeen: format(fromUnixTime(deviceInfo.last_connected_timestamp), 'MMM dd, yyyy'),
      adasHealth: deviceInfo.latest_in_use_satellite_count > 10 ? 'Healthy' : 'Limited',
      planType: deviceInfo.plan_type || 'Standard', // Added this field
      activatedSince: format(fromUnixTime(deviceInfo.active_since), 'MMM dd, yyyy'),
      appVersion: deviceInfo.application_version,
      licensePlate: deviceInfo.license_plate,
      vin: deviceInfo.vehicle_identification_number,
      vehicleTypeName: deviceInfo.product_name,
      region: deviceInfo.region,
      satellites: deviceInfo.latest_in_use_satellite_count,
      camera_height: deviceInfo.camera_height,
      cid: deviceInfo.cid,
    };
  };

  // Fetch devices data
  const fetchDevices = async () => {
    setFetching(true);
    if (!data) setLoading(true);
    setError(null);

    try {
      // Create query parameters based on filters
      const statusFilter = filters.status.length > 0 ?
        filters.status.map(s => s.toLowerCase()).join(',') :
        undefined;

      const modelFilter = filters.model.length > 0 ?
        filters.model :
        undefined;

      // Calculate offset based on page and perPage
      const offset = (page - 1) * perPage;

      // Fetch devices
      const response = await api.getDevices({
        cid: searchTerm || undefined,
        limit: perPage,
        offset
      });

      // Count status types
      const online = response.data.filter(d => d.is_online).length;
      const offline = response.data.filter(d => !d.is_online).length;

      // Set data
      setData({
        devices: response.data,
        totalCount: response.data.length,
        stats: {
          online,
          offline,
          maintenance: 0 // Not available in API
        }
      });
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to fetch devices'));
    } finally {
      setLoading(false);
      setFetching(false);
    }
  };

  // Reset to page 1 when filters or search changes
  useEffect(() => {
    setPage(1);
  }, [dateRange, filters, searchTerm]);

  // Fetch devices when dependencies change
  useEffect(() => {
    fetchDevices();
  }, [page, perPage, searchTerm, filters]);

  // Handle date range change
  const handleDateRangeChange = (startDate: Date, endDate: Date) => {
    setDateRange({ startDate, endDate });
  };

  // Handle filter change
  const handleFilterChange = (filterId: string, values: string[]) => {
    setFilters(prev => ({
      ...prev,
      [filterId]: values,
    }));
  };

  // Handle clear filters
  const handleClearFilters = () => {
    setFilters({
      status: [],
      model: [],
      lastSeen: [],
    });
    setSearchTerm('');
  };

  // Handle search
  const handleSearch = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);
    const search = formData.get('search') as string;
    setSearchTerm(search);
  };

  // Handle add device
  const handleAddDevice = () => {
    navigate('/devices/new');
  };

  // Handle refresh
  const handleRefresh = () => {
    fetchDevices();
  };

  // Filter groups
  // const filterGroups = [
  //   {
  //     id: 'status',
  //     label: 'Status',
  //     options: [
  //       { id: 'online', label: 'Online' },
  //       { id: 'offline', label: 'Offline' },
  //       { id: 'maintenance', label: 'Maintenance' },
  //     ],
  //   },
  //   {
  //     id: 'model',
  //     label: 'Model',
  //     options: [
  //       { id: 'K220', label: 'K220' },
  //       { id: 'K120', label: 'K120' },
  //       { id: 'V100', label: 'V100' },
  //       { id: 'V80', label: 'V80' },
  //     ],
  //   },
  //   {
  //     id: 'lastSeen',
  //     label: 'Last Seen',
  //     options: [
  //       { id: 'today', label: 'Today' },
  //       { id: 'yesterday', label: 'Yesterday' },
  //       { id: 'lastWeek', label: 'Last Week' },
  //       { id: 'lastMonth', label: 'Last Month' },
  //     ],
  //   },
  // ];

  return (
    <div className="space-y-6">
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
        <h1 className="text-2xl font-bold">Devices</h1>

        <div className="flex space-x-3">
          <Button
            variant="outline"
            leftIcon={<RefreshCw size={16} />}
            onClick={handleRefresh}
            isLoading={fetching}
          >
            Refresh
          </Button>
          <Button
            variant="outline"
            leftIcon={<Download size={16} />}
          >
            Export
          </Button>
          <Button
            onClick={handleAddDevice}
            leftIcon={<Plus size={16} />}
          >
            Add Device
          </Button>
        </div>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Filter size={18} className="mr-2" />
            Search and Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Search form */}
            <form onSubmit={handleSearch} className="flex space-x-2">
              <div className="relative flex-1">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Search size={16} className="text-gray-400" />
                </div>
                <input
                  type="text"
                  name="search"
                  placeholder="Search by device ID, model, or serial number..."
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md"
                  defaultValue={searchTerm}
                />
              </div>
              <Button type="submit">Search</Button>
            </form>

            {/* Filters */}
            {/* <DataFilter
              dateRange={dateRange}
              filters={filterGroups}
              activeFilters={filters}
              onDateRangeChange={handleDateRangeChange}
              onFilterChange={handleFilterChange}
              onClearFilters={handleClearFilters}
            /> */}
          </div>
        </CardContent>
      </Card>

      {/* Status Overview */}
      {!loading && !error && data?.stats && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card className="md:col-span-2">
            <CardHeader>
              <CardTitle>Device Status Overview</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-green-50 p-4 rounded-lg">
                  <div className="flex items-center">
                    <div className="p-2 bg-green-100 rounded-lg">
                      <Truck size={24} className="text-green-600" />
                    </div>
                    <div className="ml-4">
                      <div className="text-sm text-green-700 font-medium">Online</div>
                      <div className="text-2xl font-bold">{data.stats.online}</div>
                    </div>
                  </div>
                </div>

                <div className="bg-red-50 p-4 rounded-lg">
                  <div className="flex items-center">
                    <div className="p-2 bg-red-100 rounded-lg">
                      <Truck size={24} className="text-red-600" />
                    </div>
                    <div className="ml-4">
                      <div className="text-sm text-red-700 font-medium">Offline</div>
                      <div className="text-2xl font-bold">{data.stats.offline}</div>
                    </div>
                  </div>
                </div>

                <div className="bg-blue-50 p-4 rounded-lg">
                  <div className="flex items-center">
                    <div className="p-2 bg-blue-100 rounded-lg">
                      <Truck size={24} className="text-blue-600" />
                    </div>
                    <div className="ml-4">
                      <div className="text-sm text-blue-700 font-medium">Maintenance</div>
                      <div className="text-2xl font-bold">{data.stats.maintenance}</div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Status Distribution</CardTitle>
            </CardHeader>
            <CardContent>
              <DeviceStatusChart
                online={data.stats.online}
                offline={data.stats.offline}
              />
            </CardContent>
          </Card>
        </div>
      )}

      {/* Devices Table */}
      <Card>
        <CardHeader>
          <CardTitle>Device List</CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          {loading ? (
            <div className="flex justify-center items-center p-8">
              <Spinner size="large" color="primary" />
            </div>
          ) : error ? (
            <Alert
              type="error"
              title="Error Loading Devices"
              message="There was a problem loading the devices data. Please try again."
              action={{
                label: 'Retry',
                onClick: fetchDevices
              }}
              className="m-4"
            />
          ) : data?.devices.length === 0 ? (
            <div className="text-center p-8 text-gray-500">
              No devices found matching your filters.
            </div>
          ) : (
<DevicesTable
  devices={data?.devices.map(adaptDeviceInfo) || []}
  isLoading={fetching}
  onRowClick={(deviceId) => navigate(`/devices/${deviceId}`)}
/>

          )}
        </CardContent>
      </Card>

      {/* Pagination */}
      {!loading && !error && data?.totalCount && data.totalCount > 0 && (
        <div className="flex justify-between items-center">
          <div className="text-sm text-gray-500">
            Showing {data.devices.length} of {data.totalCount} devices
          </div>
          <div className="flex items-center space-x-4">
            <select
              className="px-2 py-1 border rounded-md text-sm"
              value={perPage}
              onChange={(e) => setPerPage(Number(e.target.value))}
            >
              <option value={10}>10 per page</option>
              <option value={25}>25 per page</option>
              <option value={50}>50 per page</option>
              <option value={100}>100 per page</option>
            </select>

            <Pagination
              currentPage={page}
              totalPages={Math.ceil(data.totalCount / perPage)}
              onPageChange={setPage}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default Devices;