import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, useNavigate } from 'react-router-dom';
import { format } from 'date-fns';
import { 
  ChevronLeft, MapPin, User, Clock, Calendar, 
  Route, Download, ExternalLink, AlertTriangle 
} from 'lucide-react';

import { Card, CardContent, CardHeader, CardTitle } from '../../components/common/Card';
import { Button } from '../../components/common/Button';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from '../../components/common/Tabs';
import { Spinner } from '../../components/common/Spinner';
import { Alert } from '../../components/common/Alert';
import { TripRouteMap } from '../../components/maps/TripRouteMap';
import { SpeedChart } from '../../components/charts/SpeedChart';
import { EventsDistributionChart } from '../../components/charts/EventDistributionChart';

import { useApi } from '../../contexts/ApiContext';
import { 
  NewEventsResponse,
  TripDetailResponse, 
} from '../../api/visionMaxApi';

// Event interface matching API response
interface TripEvent {
  id: string;
  type: number;
  name: string;
  severity_level: string;
  time: number;
  driver_id: number;
  driver_name: string;
  asset_id: string;
  lat: string;
  lng: string;
  speed?: number;
  position: {
    lat: number;
    lng: number;
  };
  timestamp: string;
}

const TripDetails: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { api } = useApi();
  
  // State management
  const [trip, setTrip] = useState<TripDetailResponse['data'] | null>(null);
  const [events, setEvents] = useState<any>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  
  // Tab and event selection state
  const [activeTab, setActiveTab] = useState('overview');
  const [selectedEventId, setSelectedEventId] = useState<string | null>(null);
  
  // Fetch trip details and events
  useEffect(() => {
    const fetchTripData = async () => {
      if (!id) return;
      
      try {
        setIsLoading(true);
        
        // Fetch trip details
        const tripResponse = await api.getTripDetail(Number(id));
        setTrip(tripResponse.data);
        
        // Fetch events for this trip
        const eventsResponse = await api.getEvents({
          since: tripResponse.data.start_time,
          until: tripResponse.data.end_time,
          keyword: tripResponse.data.asset_id
        });
        setEvents(eventsResponse.data);
        
        setError(null);
      } catch (err) {
        setError(err instanceof Error ? err : new Error('Failed to fetch trip details'));
        setTrip(null);
        setEvents([]);
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchTripData();
  }, [id, api]);
  
  // Handle back navigation
  const handleBack = () => {
    navigate('/trips');
  };
  
  // Handle download report
  const handleDownloadReport = () => {
    alert('Downloading trip report...');
  };
  
  // Format duration (HH:MM:SS)
  const formatDuration = (durationInSeconds: number) => {
    const hours = Math.floor(durationInSeconds / 3600);
    const minutes = Math.floor((durationInSeconds % 3600) / 60);
    const seconds = Math.floor(durationInSeconds % 60);
    
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };
  
  // Render loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-96">
        <Spinner size="large" />
      </div>
    );
  }
  
  // Render error state
  if (error) {
    return (
      <Alert
        type="error"
        title="Error Loading Trip"
        message="There was a problem loading the trip data. Please try again."
        action={{
          label: 'Retry',
          onClick: () => window.location.reload()
        }}
      />
    );
  }
  
  // Render not found state
  if (!trip) {
    return (
      <Alert
        type="error"
        title="Trip Not Found"
        message={`No trip found with ID: ${id}`}
        action={{
          label: 'Back to Trips',
          onClick: handleBack
        }}
      />
    );
  }
  
  return (
    <div className="space-y-6">
      {/* Header with back button and actions */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0">
        <div className="flex items-center">
          <Button variant="ghost" onClick={handleBack} className="mr-4">
            <ChevronLeft size={16} className="mr-1" />
            Back
          </Button>
          
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Trip Details</h1>
            <div className="flex items-center mt-1">
              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                trip.status === 'completed' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
              }`}>
                {trip.status}
              </span>
              <span className="mx-2 text-gray-300">|</span>
              <span className="text-sm text-gray-500">Trip ID: {trip.id}</span>
            </div>
          </div>
        </div>
        
        <div className="flex space-x-3">
          <Button variant="outline" onClick={handleDownloadReport}>
            <Download size={16} className="mr-2" />
            Download Report
          </Button>
          <Button>
            <ExternalLink size={16} className="mr-2" />
            Share
          </Button>
        </div>
      </div>
      
      {/* Trip info cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
        <Card className="md:col-span-2">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm text-gray-500 flex items-center">
              <Calendar size={16} className="mr-2" />
              Date & Time
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-lg font-medium">
              {format(new Date(trip.start_time * 1000), 'MMM d, yyyy h:mm a')}
              {trip.end_time && (
                <>
                  <span className="mx-2">-</span>
                  {format(new Date(trip.end_time * 1000), 'h:mm a')}
                </>
              )}
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm text-gray-500 flex items-center">
              <User size={16} className="mr-2" />
              Driver
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-lg font-medium">
              {trip.driver_name || 'Unknown'}
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm text-gray-500 flex items-center">
              <Route size={16} className="mr-2" />
              Distance
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-lg font-medium">
              {trip.mileage.toFixed(2)} km
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm text-gray-500 flex items-center">
              <Clock size={16} className="mr-2" />
              Duration
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-lg font-medium">
              {formatDuration(trip.duration)}
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm text-gray-500 flex items-center">
              <AlertTriangle size={16} className="mr-2" />
              Events
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-lg font-medium">
              {events.length}
            </div>
          </CardContent>
        </Card>
      </div>
      
      {/* Tabs for different views */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid grid-cols-3 w-full max-w-lg">
          <TabsTrigger value="overview" className="flex items-center">
            <MapPin size={16} className="mr-2" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="telemetry" className="flex items-center">
            <Route size={16} className="mr-2" />
            Telemetry
          </TabsTrigger>
          <TabsTrigger value="events" className="flex items-center">
            <AlertTriangle size={16} className="mr-2" />
            Events
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="overview" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Route Map</CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              <TripRouteMap
                route={[]} // Placeholder, update with actual route data if available
                startLocation={{
                  lat: trip.gps?.lat,
                  lng: trip.gps?.lng,
                  address: 'Unknown'
                }}
                endLocation={undefined} // Add end location if available
                events={events}
                selectedEventId={selectedEventId || undefined}
                onEventClick={(id) => setSelectedEventId(id)}
              />
            </CardContent>
          </Card>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
            <Card>
              <CardHeader>
                <CardTitle>Trip Information</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between">
                    <span className="text-gray-500">Vehicle</span>
                    <span className="font-medium">{trip.asset_id}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-500">Start Location</span>
                    <span className="font-medium">
                      {`${trip.gps?.lat || ''}, ${trip.gps?.lng || ''}`}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-500">Speed</span>
                    <span className="font-medium">
                      {trip.gps?.speed || 'N/A'} km/h
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>Event Summary</CardTitle>
              </CardHeader>
              <CardContent>
                {events.length > 0 ? (
                  <>
                    <EventsDistributionChart
                      data={[
                        {
                          type: 'Hard Braking',
                          count: events.filter(e => e.name === 'Hard Braking').length,
                          color: '#EF4444',
                        },
                        {
                          type: 'Speeding',
                          count: events.filter(e => e.name === 'Speeding').length,
                          color: '#F97316',
                        },
                        {
                          type: 'Hard Acceleration',
                          count: events.filter(e => e.name === 'Hard Acceleration').length,
                          color: '#F59E0B',
                        },
                        {
                          type: 'Other',
                          count: events.filter(e => 
                            !['Hard Braking', 'Speeding', 'Hard Acceleration'].includes(e.name)
                          ).length,
                          color: '#6B7280',
                        },
                      ]}
                    />
                    
                    <div className="mt-4">
                      <div className="text-sm font-medium mb-2">Recent Events</div>
                      <div className="space-y-2">
                        {events.slice(0, 3).map((event) => (
                          <div 
                            key={event.id} 
                            className="flex items-center justify-between p-2 bg-gray-50 rounded-md cursor-pointer hover:bg-gray-100"
                            onClick={() => setSelectedEventId(event.id)}
                          >
                            <div className="flex items-center">
                              <div 
                                className={`w-2 h-2 rounded-full mr-2 ${
                                  event.severity_level === 'high' ? 'bg-red-500' :
                                  event.severity_level === 'medium' ? 'bg-yellow-500' :
                                  'bg-green-500'
                                }`}
                              />
                              <span>{event.name}</span>
                            </div>
                            <span className="text-sm text-gray-500">
                              {format(new Date(event.time * 1000), 'h:mm a')}
                            </span>
                          </div>
                        ))}
                      </div>
                      
                      {events.length > 3 && (
                        <button 
                          className="text-sm text-blue-600 mt-2 hover:underline"
                          onClick={() => setActiveTab('events')}
                        >
                          View all {events.length} events
                        </button>
                      )}
                    </div>
                  </>
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    No events recorded for this trip
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        
        <TabsContent value="telemetry" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Speed Profile</CardTitle>
            </CardHeader>
            <CardContent>
              <SpeedChart
              data={[]} // Placeholder for speed data
              speedLimit={undefined} // Add speed limit if available from API
              showAcceleration={true}
            />
          </CardContent>
        </Card>
      </TabsContent>
      
      <TabsContent value="events" className="mt-6">
        <Card>
          <CardHeader>
            <CardTitle>Trip Events</CardTitle>
          </CardHeader>
          <CardContent>
            {events.length > 0 ? (
              <div className="divide-y">
                {events.map((event) => (
                  <div 
                    key={event.id}
                    className={`py-4 flex flex-col sm:flex-row sm:items-center sm:justify-between cursor-pointer ${
                      selectedEventId === event.id ? 'bg-blue-50' : ''
                    }`}
                    onClick={() => setSelectedEventId(event.id)}
                  >
                    <div className="flex items-center mb-2 sm:mb-0">
                      <div 
                        className={`w-3 h-3 rounded-full mr-3 ${
                          event.severity_level === 'high' ? 'bg-red-500' :
                          event.severity_level === 'medium' ? 'bg-yellow-500' :
                          'bg-green-500'
                        }`}
                      />
                      <div>
                        <div className="font-medium">
                          {event.name}
                        </div>
                        <div className="text-sm text-gray-500">
                          {format(new Date(event.time * 1000), 'MMM d, yyyy h:mm:ss a')}
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center">
                      {event.speed !== undefined && (
                        <div className="text-sm mr-4">
                          <span className="text-gray-500">Speed:</span>{' '}
                          <span className="font-medium">{event.speed} km/h</span>
                        </div>
                      )}
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          // Navigate to event details or show modal
                          navigate(`/events/${event.id}`);
                        }}
                      >
                        View Details
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                No events recorded for this trip
              </div>
            )}
          </CardContent>
        </Card>
      </TabsContent>
    </Tabs>
  </div>
);
};

export default TripDetails;