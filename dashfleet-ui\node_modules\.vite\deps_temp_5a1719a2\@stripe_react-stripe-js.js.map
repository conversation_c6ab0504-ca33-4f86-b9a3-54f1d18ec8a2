{"version": 3, "sources": ["../../@stripe/react-stripe-js/dist/react-stripe.esm.mjs"], "sourcesContent": ["import React from 'react';\nimport PropTypes from 'prop-types';\n\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n\n    if (enumerableOnly) {\n      symbols = symbols.filter(function (sym) {\n        return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n      });\n    }\n\n    keys.push.apply(keys, symbols);\n  }\n\n  return keys;\n}\n\nfunction _objectSpread2(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n\n    if (i % 2) {\n      ownKeys(Object(source), true).forEach(function (key) {\n        _defineProperty(target, key, source[key]);\n      });\n    } else if (Object.getOwnPropertyDescriptors) {\n      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n    } else {\n      ownKeys(Object(source)).forEach(function (key) {\n        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n      });\n    }\n  }\n\n  return target;\n}\n\nfunction _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") {\n    _typeof = function (obj) {\n      return typeof obj;\n    };\n  } else {\n    _typeof = function (obj) {\n      return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n    };\n  }\n\n  return _typeof(obj);\n}\n\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n\n  return obj;\n}\n\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n\n  return target;\n}\n\nfunction _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n\n  var target = _objectWithoutPropertiesLoose(source, excluded);\n\n  var key, i;\n\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n\n  return target;\n}\n\nfunction _slicedToArray(arr, i) {\n  return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();\n}\n\nfunction _arrayWithHoles(arr) {\n  if (Array.isArray(arr)) return arr;\n}\n\nfunction _iterableToArrayLimit(arr, i) {\n  var _i = arr && (typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"]);\n\n  if (_i == null) return;\n  var _arr = [];\n  var _n = true;\n  var _d = false;\n\n  var _s, _e;\n\n  try {\n    for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) {\n      _arr.push(_s.value);\n\n      if (i && _arr.length === i) break;\n    }\n  } catch (err) {\n    _d = true;\n    _e = err;\n  } finally {\n    try {\n      if (!_n && _i[\"return\"] != null) _i[\"return\"]();\n    } finally {\n      if (_d) throw _e;\n    }\n  }\n\n  return _arr;\n}\n\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\n\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n\n  return arr2;\n}\n\nfunction _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\nvar useAttachEvent = function useAttachEvent(element, event, cb) {\n  var cbDefined = !!cb;\n  var cbRef = React.useRef(cb); // In many integrations the callback prop changes on each render.\n  // Using a ref saves us from calling element.on/.off every render.\n\n  React.useEffect(function () {\n    cbRef.current = cb;\n  }, [cb]);\n  React.useEffect(function () {\n    if (!cbDefined || !element) {\n      return function () {};\n    }\n\n    var decoratedCb = function decoratedCb() {\n      if (cbRef.current) {\n        cbRef.current.apply(cbRef, arguments);\n      }\n    };\n\n    element.on(event, decoratedCb);\n    return function () {\n      element.off(event, decoratedCb);\n    };\n  }, [cbDefined, event, element, cbRef]);\n};\n\nvar usePrevious = function usePrevious(value) {\n  var ref = React.useRef(value);\n  React.useEffect(function () {\n    ref.current = value;\n  }, [value]);\n  return ref.current;\n};\n\nvar isUnknownObject = function isUnknownObject(raw) {\n  return raw !== null && _typeof(raw) === 'object';\n};\nvar isPromise = function isPromise(raw) {\n  return isUnknownObject(raw) && typeof raw.then === 'function';\n}; // We are using types to enforce the `stripe` prop in this lib,\n// but in an untyped integration `stripe` could be anything, so we need\n// to do some sanity validation to prevent type errors.\n\nvar isStripe = function isStripe(raw) {\n  return isUnknownObject(raw) && typeof raw.elements === 'function' && typeof raw.createToken === 'function' && typeof raw.createPaymentMethod === 'function' && typeof raw.confirmCardPayment === 'function';\n};\n\nvar PLAIN_OBJECT_STR = '[object Object]';\nvar isEqual = function isEqual(left, right) {\n  if (!isUnknownObject(left) || !isUnknownObject(right)) {\n    return left === right;\n  }\n\n  var leftArray = Array.isArray(left);\n  var rightArray = Array.isArray(right);\n  if (leftArray !== rightArray) return false;\n  var leftPlainObject = Object.prototype.toString.call(left) === PLAIN_OBJECT_STR;\n  var rightPlainObject = Object.prototype.toString.call(right) === PLAIN_OBJECT_STR;\n  if (leftPlainObject !== rightPlainObject) return false; // not sure what sort of special object this is (regexp is one option), so\n  // fallback to reference check.\n\n  if (!leftPlainObject && !leftArray) return left === right;\n  var leftKeys = Object.keys(left);\n  var rightKeys = Object.keys(right);\n  if (leftKeys.length !== rightKeys.length) return false;\n  var keySet = {};\n\n  for (var i = 0; i < leftKeys.length; i += 1) {\n    keySet[leftKeys[i]] = true;\n  }\n\n  for (var _i = 0; _i < rightKeys.length; _i += 1) {\n    keySet[rightKeys[_i]] = true;\n  }\n\n  var allKeys = Object.keys(keySet);\n\n  if (allKeys.length !== leftKeys.length) {\n    return false;\n  }\n\n  var l = left;\n  var r = right;\n\n  var pred = function pred(key) {\n    return isEqual(l[key], r[key]);\n  };\n\n  return allKeys.every(pred);\n};\n\nvar extractAllowedOptionsUpdates = function extractAllowedOptionsUpdates(options, prevOptions, immutableKeys) {\n  if (!isUnknownObject(options)) {\n    return null;\n  }\n\n  return Object.keys(options).reduce(function (newOptions, key) {\n    var isUpdated = !isUnknownObject(prevOptions) || !isEqual(options[key], prevOptions[key]);\n\n    if (immutableKeys.includes(key)) {\n      if (isUpdated) {\n        console.warn(\"Unsupported prop change: options.\".concat(key, \" is not a mutable property.\"));\n      }\n\n      return newOptions;\n    }\n\n    if (!isUpdated) {\n      return newOptions;\n    }\n\n    return _objectSpread2(_objectSpread2({}, newOptions || {}), {}, _defineProperty({}, key, options[key]));\n  }, null);\n};\n\nvar INVALID_STRIPE_ERROR$2 = 'Invalid prop `stripe` supplied to `Elements`. We recommend using the `loadStripe` utility from `@stripe/stripe-js`. See https://stripe.com/docs/stripe-js/react#elements-props-stripe for details.'; // We are using types to enforce the `stripe` prop in this lib, but in a real\n// integration `stripe` could be anything, so we need to do some sanity\n// validation to prevent type errors.\n\nvar validateStripe = function validateStripe(maybeStripe) {\n  var errorMsg = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : INVALID_STRIPE_ERROR$2;\n\n  if (maybeStripe === null || isStripe(maybeStripe)) {\n    return maybeStripe;\n  }\n\n  throw new Error(errorMsg);\n};\n\nvar parseStripeProp = function parseStripeProp(raw) {\n  var errorMsg = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : INVALID_STRIPE_ERROR$2;\n\n  if (isPromise(raw)) {\n    return {\n      tag: 'async',\n      stripePromise: Promise.resolve(raw).then(function (result) {\n        return validateStripe(result, errorMsg);\n      })\n    };\n  }\n\n  var stripe = validateStripe(raw, errorMsg);\n\n  if (stripe === null) {\n    return {\n      tag: 'empty'\n    };\n  }\n\n  return {\n    tag: 'sync',\n    stripe: stripe\n  };\n};\n\nvar registerWithStripeJs = function registerWithStripeJs(stripe) {\n  if (!stripe || !stripe._registerWrapper || !stripe.registerAppInfo) {\n    return;\n  }\n\n  stripe._registerWrapper({\n    name: 'react-stripe-js',\n    version: \"3.7.0\"\n  });\n\n  stripe.registerAppInfo({\n    name: 'react-stripe-js',\n    version: \"3.7.0\",\n    url: 'https://stripe.com/docs/stripe-js/react'\n  });\n};\n\nvar ElementsContext = /*#__PURE__*/React.createContext(null);\nElementsContext.displayName = 'ElementsContext';\nvar parseElementsContext = function parseElementsContext(ctx, useCase) {\n  if (!ctx) {\n    throw new Error(\"Could not find Elements context; You need to wrap the part of your app that \".concat(useCase, \" in an <Elements> provider.\"));\n  }\n\n  return ctx;\n};\n/**\n * The `Elements` provider allows you to use [Element components](https://stripe.com/docs/stripe-js/react#element-components) and access the [Stripe object](https://stripe.com/docs/js/initializing) in any nested component.\n * Render an `Elements` provider at the root of your React app so that it is available everywhere you need it.\n *\n * To use the `Elements` provider, call `loadStripe` from `@stripe/stripe-js` with your publishable key.\n * The `loadStripe` function will asynchronously load the Stripe.js script and initialize a `Stripe` object.\n * Pass the returned `Promise` to `Elements`.\n *\n * @docs https://stripe.com/docs/stripe-js/react#elements-provider\n */\n\nvar Elements = function Elements(_ref) {\n  var rawStripeProp = _ref.stripe,\n      options = _ref.options,\n      children = _ref.children;\n  var parsed = React.useMemo(function () {\n    return parseStripeProp(rawStripeProp);\n  }, [rawStripeProp]); // For a sync stripe instance, initialize into context\n\n  var _React$useState = React.useState(function () {\n    return {\n      stripe: parsed.tag === 'sync' ? parsed.stripe : null,\n      elements: parsed.tag === 'sync' ? parsed.stripe.elements(options) : null\n    };\n  }),\n      _React$useState2 = _slicedToArray(_React$useState, 2),\n      ctx = _React$useState2[0],\n      setContext = _React$useState2[1];\n\n  React.useEffect(function () {\n    var isMounted = true;\n\n    var safeSetContext = function safeSetContext(stripe) {\n      setContext(function (ctx) {\n        // no-op if we already have a stripe instance (https://github.com/stripe/react-stripe-js/issues/296)\n        if (ctx.stripe) return ctx;\n        return {\n          stripe: stripe,\n          elements: stripe.elements(options)\n        };\n      });\n    }; // For an async stripePromise, store it in context once resolved\n\n\n    if (parsed.tag === 'async' && !ctx.stripe) {\n      parsed.stripePromise.then(function (stripe) {\n        if (stripe && isMounted) {\n          // Only update Elements context if the component is still mounted\n          // and stripe is not null. We allow stripe to be null to make\n          // handling SSR easier.\n          safeSetContext(stripe);\n        }\n      });\n    } else if (parsed.tag === 'sync' && !ctx.stripe) {\n      // Or, handle a sync stripe instance going from null -> populated\n      safeSetContext(parsed.stripe);\n    }\n\n    return function () {\n      isMounted = false;\n    };\n  }, [parsed, ctx, options]); // Warn on changes to stripe prop\n\n  var prevStripe = usePrevious(rawStripeProp);\n  React.useEffect(function () {\n    if (prevStripe !== null && prevStripe !== rawStripeProp) {\n      console.warn('Unsupported prop change on Elements: You cannot change the `stripe` prop after setting it.');\n    }\n  }, [prevStripe, rawStripeProp]); // Apply updates to elements when options prop has relevant changes\n\n  var prevOptions = usePrevious(options);\n  React.useEffect(function () {\n    if (!ctx.elements) {\n      return;\n    }\n\n    var updates = extractAllowedOptionsUpdates(options, prevOptions, ['clientSecret', 'fonts']);\n\n    if (updates) {\n      ctx.elements.update(updates);\n    }\n  }, [options, prevOptions, ctx.elements]); // Attach react-stripe-js version to stripe.js instance\n\n  React.useEffect(function () {\n    registerWithStripeJs(ctx.stripe);\n  }, [ctx.stripe]);\n  return /*#__PURE__*/React.createElement(ElementsContext.Provider, {\n    value: ctx\n  }, children);\n};\nElements.propTypes = {\n  stripe: PropTypes.any,\n  options: PropTypes.object\n};\nvar useElementsContextWithUseCase = function useElementsContextWithUseCase(useCaseMessage) {\n  var ctx = React.useContext(ElementsContext);\n  return parseElementsContext(ctx, useCaseMessage);\n};\n/**\n * @docs https://stripe.com/docs/stripe-js/react#useelements-hook\n */\n\nvar useElements = function useElements() {\n  var _useElementsContextWi = useElementsContextWithUseCase('calls useElements()'),\n      elements = _useElementsContextWi.elements;\n\n  return elements;\n};\n/**\n * @docs https://stripe.com/docs/stripe-js/react#elements-consumer\n */\n\nvar ElementsConsumer = function ElementsConsumer(_ref2) {\n  var children = _ref2.children;\n  var ctx = useElementsContextWithUseCase('mounts <ElementsConsumer>'); // Assert to satisfy the busted React.FC return type (it should be ReactNode)\n\n  return children(ctx);\n};\nElementsConsumer.propTypes = {\n  children: PropTypes.func.isRequired\n};\n\nvar _excluded$1 = [\"on\", \"session\"];\nvar CheckoutSdkContext = /*#__PURE__*/React.createContext(null);\nCheckoutSdkContext.displayName = 'CheckoutSdkContext';\nvar parseCheckoutSdkContext = function parseCheckoutSdkContext(ctx, useCase) {\n  if (!ctx) {\n    throw new Error(\"Could not find CheckoutProvider context; You need to wrap the part of your app that \".concat(useCase, \" in an <CheckoutProvider> provider.\"));\n  }\n\n  return ctx;\n};\nvar CheckoutContext = /*#__PURE__*/React.createContext(null);\nCheckoutContext.displayName = 'CheckoutContext';\nvar extractCheckoutContextValue = function extractCheckoutContextValue(checkoutSdk, sessionState) {\n  if (!checkoutSdk) {\n    return null;\n  }\n\n  checkoutSdk.on;\n      checkoutSdk.session;\n      var actions = _objectWithoutProperties(checkoutSdk, _excluded$1);\n\n  if (!sessionState) {\n    return Object.assign(checkoutSdk.session(), actions);\n  }\n\n  return Object.assign(sessionState, actions);\n};\nvar INVALID_STRIPE_ERROR$1 = 'Invalid prop `stripe` supplied to `CheckoutProvider`. We recommend using the `loadStripe` utility from `@stripe/stripe-js`. See https://stripe.com/docs/stripe-js/react#elements-props-stripe for details.';\nvar CheckoutProvider = function CheckoutProvider(_ref) {\n  var rawStripeProp = _ref.stripe,\n      options = _ref.options,\n      children = _ref.children;\n  var parsed = React.useMemo(function () {\n    return parseStripeProp(rawStripeProp, INVALID_STRIPE_ERROR$1);\n  }, [rawStripeProp]); // State used to trigger a re-render when sdk.session is updated\n\n  var _React$useState = React.useState(null),\n      _React$useState2 = _slicedToArray(_React$useState, 2),\n      session = _React$useState2[0],\n      setSession = _React$useState2[1];\n\n  var _React$useState3 = React.useState(function () {\n    return {\n      stripe: parsed.tag === 'sync' ? parsed.stripe : null,\n      checkoutSdk: null\n    };\n  }),\n      _React$useState4 = _slicedToArray(_React$useState3, 2),\n      ctx = _React$useState4[0],\n      setContext = _React$useState4[1];\n\n  var safeSetContext = function safeSetContext(stripe, checkoutSdk) {\n    setContext(function (ctx) {\n      if (ctx.stripe && ctx.checkoutSdk) {\n        return ctx;\n      }\n\n      return {\n        stripe: stripe,\n        checkoutSdk: checkoutSdk\n      };\n    });\n  }; // Ref used to avoid calling initCheckout multiple times when options changes\n\n\n  var initCheckoutCalledRef = React.useRef(false);\n  React.useEffect(function () {\n    var isMounted = true;\n\n    if (parsed.tag === 'async' && !ctx.stripe) {\n      parsed.stripePromise.then(function (stripe) {\n        if (stripe && isMounted && !initCheckoutCalledRef.current) {\n          // Only update context if the component is still mounted\n          // and stripe is not null. We allow stripe to be null to make\n          // handling SSR easier.\n          initCheckoutCalledRef.current = true;\n          stripe.initCheckout(options).then(function (checkoutSdk) {\n            if (checkoutSdk) {\n              safeSetContext(stripe, checkoutSdk);\n              checkoutSdk.on('change', setSession);\n            }\n          });\n        }\n      });\n    } else if (parsed.tag === 'sync' && parsed.stripe && !initCheckoutCalledRef.current) {\n      initCheckoutCalledRef.current = true;\n      parsed.stripe.initCheckout(options).then(function (checkoutSdk) {\n        if (checkoutSdk) {\n          safeSetContext(parsed.stripe, checkoutSdk);\n          checkoutSdk.on('change', setSession);\n        }\n      });\n    }\n\n    return function () {\n      isMounted = false;\n    };\n  }, [parsed, ctx, options, setSession]); // Warn on changes to stripe prop\n\n  var prevStripe = usePrevious(rawStripeProp);\n  React.useEffect(function () {\n    if (prevStripe !== null && prevStripe !== rawStripeProp) {\n      console.warn('Unsupported prop change on CheckoutProvider: You cannot change the `stripe` prop after setting it.');\n    }\n  }, [prevStripe, rawStripeProp]); // Apply updates to elements when options prop has relevant changes\n\n  var prevOptions = usePrevious(options);\n  var prevCheckoutSdk = usePrevious(ctx.checkoutSdk);\n  React.useEffect(function () {\n    var _prevOptions$elements, _options$elementsOpti;\n\n    // Ignore changes while checkout sdk is not initialized.\n    if (!ctx.checkoutSdk) {\n      return;\n    }\n\n    var previousAppearance = prevOptions === null || prevOptions === void 0 ? void 0 : (_prevOptions$elements = prevOptions.elementsOptions) === null || _prevOptions$elements === void 0 ? void 0 : _prevOptions$elements.appearance;\n    var currentAppearance = options === null || options === void 0 ? void 0 : (_options$elementsOpti = options.elementsOptions) === null || _options$elementsOpti === void 0 ? void 0 : _options$elementsOpti.appearance;\n    var hasAppearanceChanged = !isEqual(currentAppearance, previousAppearance);\n    var hasSdkLoaded = !prevCheckoutSdk && ctx.checkoutSdk;\n\n    if (currentAppearance && (hasAppearanceChanged || hasSdkLoaded)) {\n      ctx.checkoutSdk.changeAppearance(currentAppearance);\n    }\n  }, [options, prevOptions, ctx.checkoutSdk, prevCheckoutSdk]); // Attach react-stripe-js version to stripe.js instance\n\n  React.useEffect(function () {\n    registerWithStripeJs(ctx.stripe);\n  }, [ctx.stripe]);\n  var checkoutContextValue = React.useMemo(function () {\n    return extractCheckoutContextValue(ctx.checkoutSdk, session);\n  }, [ctx.checkoutSdk, session]);\n\n  if (!ctx.checkoutSdk) {\n    return null;\n  }\n\n  return /*#__PURE__*/React.createElement(CheckoutSdkContext.Provider, {\n    value: ctx\n  }, /*#__PURE__*/React.createElement(CheckoutContext.Provider, {\n    value: checkoutContextValue\n  }, children));\n};\nCheckoutProvider.propTypes = {\n  stripe: PropTypes.any,\n  options: PropTypes.shape({\n    fetchClientSecret: PropTypes.func.isRequired,\n    elementsOptions: PropTypes.object\n  }).isRequired\n};\nvar useCheckoutSdkContextWithUseCase = function useCheckoutSdkContextWithUseCase(useCaseString) {\n  var ctx = React.useContext(CheckoutSdkContext);\n  return parseCheckoutSdkContext(ctx, useCaseString);\n};\nvar useElementsOrCheckoutSdkContextWithUseCase = function useElementsOrCheckoutSdkContextWithUseCase(useCaseString) {\n  var checkoutSdkContext = React.useContext(CheckoutSdkContext);\n  var elementsContext = React.useContext(ElementsContext);\n\n  if (checkoutSdkContext && elementsContext) {\n    throw new Error(\"You cannot wrap the part of your app that \".concat(useCaseString, \" in both <CheckoutProvider> and <Elements> providers.\"));\n  }\n\n  if (checkoutSdkContext) {\n    return parseCheckoutSdkContext(checkoutSdkContext, useCaseString);\n  }\n\n  return parseElementsContext(elementsContext, useCaseString);\n};\nvar useCheckout = function useCheckout() {\n  // ensure it's in CheckoutProvider\n  useCheckoutSdkContextWithUseCase('calls useCheckout()');\n  var ctx = React.useContext(CheckoutContext);\n\n  if (!ctx) {\n    throw new Error('Could not find Checkout Context; You need to wrap the part of your app that calls useCheckout() in an <CheckoutProvider> provider.');\n  }\n\n  return ctx;\n};\n\nvar _excluded = [\"mode\"];\n\nvar capitalized = function capitalized(str) {\n  return str.charAt(0).toUpperCase() + str.slice(1);\n};\n\nvar createElementComponent = function createElementComponent(type, isServer) {\n  var displayName = \"\".concat(capitalized(type), \"Element\");\n\n  var ClientElement = function ClientElement(_ref) {\n    var id = _ref.id,\n        className = _ref.className,\n        _ref$options = _ref.options,\n        options = _ref$options === void 0 ? {} : _ref$options,\n        onBlur = _ref.onBlur,\n        onFocus = _ref.onFocus,\n        onReady = _ref.onReady,\n        onChange = _ref.onChange,\n        onEscape = _ref.onEscape,\n        onClick = _ref.onClick,\n        onLoadError = _ref.onLoadError,\n        onLoaderStart = _ref.onLoaderStart,\n        onNetworksChange = _ref.onNetworksChange,\n        onConfirm = _ref.onConfirm,\n        onCancel = _ref.onCancel,\n        onShippingAddressChange = _ref.onShippingAddressChange,\n        onShippingRateChange = _ref.onShippingRateChange;\n    var ctx = useElementsOrCheckoutSdkContextWithUseCase(\"mounts <\".concat(displayName, \">\"));\n    var elements = 'elements' in ctx ? ctx.elements : null;\n    var checkoutSdk = 'checkoutSdk' in ctx ? ctx.checkoutSdk : null;\n\n    var _React$useState = React.useState(null),\n        _React$useState2 = _slicedToArray(_React$useState, 2),\n        element = _React$useState2[0],\n        setElement = _React$useState2[1];\n\n    var elementRef = React.useRef(null);\n    var domNode = React.useRef(null); // For every event where the merchant provides a callback, call element.on\n    // with that callback. If the merchant ever changes the callback, removes\n    // the old callback with element.off and then call element.on with the new one.\n\n    useAttachEvent(element, 'blur', onBlur);\n    useAttachEvent(element, 'focus', onFocus);\n    useAttachEvent(element, 'escape', onEscape);\n    useAttachEvent(element, 'click', onClick);\n    useAttachEvent(element, 'loaderror', onLoadError);\n    useAttachEvent(element, 'loaderstart', onLoaderStart);\n    useAttachEvent(element, 'networkschange', onNetworksChange);\n    useAttachEvent(element, 'confirm', onConfirm);\n    useAttachEvent(element, 'cancel', onCancel);\n    useAttachEvent(element, 'shippingaddresschange', onShippingAddressChange);\n    useAttachEvent(element, 'shippingratechange', onShippingRateChange);\n    useAttachEvent(element, 'change', onChange);\n    var readyCallback;\n\n    if (onReady) {\n      if (type === 'expressCheckout') {\n        // Passes through the event, which includes visible PM types\n        readyCallback = onReady;\n      } else {\n        // For other Elements, pass through the Element itself.\n        readyCallback = function readyCallback() {\n          onReady(element);\n        };\n      }\n    }\n\n    useAttachEvent(element, 'ready', readyCallback);\n    React.useLayoutEffect(function () {\n      if (elementRef.current === null && domNode.current !== null && (elements || checkoutSdk)) {\n        var newElement = null;\n\n        if (checkoutSdk) {\n          switch (type) {\n            case 'payment':\n              newElement = checkoutSdk.createPaymentElement(options);\n              break;\n\n            case 'address':\n              if ('mode' in options) {\n                var mode = options.mode,\n                    restOptions = _objectWithoutProperties(options, _excluded);\n\n                if (mode === 'shipping') {\n                  newElement = checkoutSdk.createShippingAddressElement(restOptions);\n                } else if (mode === 'billing') {\n                  newElement = checkoutSdk.createBillingAddressElement(restOptions);\n                } else {\n                  throw new Error(\"Invalid options.mode. mode must be 'billing' or 'shipping'.\");\n                }\n              } else {\n                throw new Error(\"You must supply options.mode. mode must be 'billing' or 'shipping'.\");\n              }\n\n              break;\n\n            case 'expressCheckout':\n              newElement = checkoutSdk.createExpressCheckoutElement(options);\n              break;\n\n            case 'currencySelector':\n              newElement = checkoutSdk.createCurrencySelectorElement();\n              break;\n\n            default:\n              throw new Error(\"Invalid Element type \".concat(displayName, \". You must use either the <PaymentElement />, <AddressElement options={{mode: 'shipping'}} />, <AddressElement options={{mode: 'billing'}} />, or <ExpressCheckoutElement />.\"));\n          }\n        } else if (elements) {\n          newElement = elements.create(type, options);\n        } // Store element in a ref to ensure it's _immediately_ available in cleanup hooks in StrictMode\n\n\n        elementRef.current = newElement; // Store element in state to facilitate event listener attachment\n\n        setElement(newElement);\n\n        if (newElement) {\n          newElement.mount(domNode.current);\n        }\n      }\n    }, [elements, checkoutSdk, options]);\n    var prevOptions = usePrevious(options);\n    React.useEffect(function () {\n      if (!elementRef.current) {\n        return;\n      }\n\n      var updates = extractAllowedOptionsUpdates(options, prevOptions, ['paymentRequest']);\n\n      if (updates && 'update' in elementRef.current) {\n        elementRef.current.update(updates);\n      }\n    }, [options, prevOptions]);\n    React.useLayoutEffect(function () {\n      return function () {\n        if (elementRef.current && typeof elementRef.current.destroy === 'function') {\n          try {\n            elementRef.current.destroy();\n            elementRef.current = null;\n          } catch (error) {// Do nothing\n          }\n        }\n      };\n    }, []);\n    return /*#__PURE__*/React.createElement(\"div\", {\n      id: id,\n      className: className,\n      ref: domNode\n    });\n  }; // Only render the Element wrapper in a server environment.\n\n\n  var ServerElement = function ServerElement(props) {\n    useElementsOrCheckoutSdkContextWithUseCase(\"mounts <\".concat(displayName, \">\"));\n    var id = props.id,\n        className = props.className;\n    return /*#__PURE__*/React.createElement(\"div\", {\n      id: id,\n      className: className\n    });\n  };\n\n  var Element = isServer ? ServerElement : ClientElement;\n  Element.propTypes = {\n    id: PropTypes.string,\n    className: PropTypes.string,\n    onChange: PropTypes.func,\n    onBlur: PropTypes.func,\n    onFocus: PropTypes.func,\n    onReady: PropTypes.func,\n    onEscape: PropTypes.func,\n    onClick: PropTypes.func,\n    onLoadError: PropTypes.func,\n    onLoaderStart: PropTypes.func,\n    onNetworksChange: PropTypes.func,\n    onConfirm: PropTypes.func,\n    onCancel: PropTypes.func,\n    onShippingAddressChange: PropTypes.func,\n    onShippingRateChange: PropTypes.func,\n    options: PropTypes.object\n  };\n  Element.displayName = displayName;\n  Element.__elementType = type;\n  return Element;\n};\n\nvar isServer = typeof window === 'undefined';\n\nvar EmbeddedCheckoutContext = /*#__PURE__*/React.createContext(null);\nEmbeddedCheckoutContext.displayName = 'EmbeddedCheckoutProviderContext';\nvar useEmbeddedCheckoutContext = function useEmbeddedCheckoutContext() {\n  var ctx = React.useContext(EmbeddedCheckoutContext);\n\n  if (!ctx) {\n    throw new Error('<EmbeddedCheckout> must be used within <EmbeddedCheckoutProvider>');\n  }\n\n  return ctx;\n};\nvar INVALID_STRIPE_ERROR = 'Invalid prop `stripe` supplied to `EmbeddedCheckoutProvider`. We recommend using the `loadStripe` utility from `@stripe/stripe-js`. See https://stripe.com/docs/stripe-js/react#elements-props-stripe for details.';\nvar EmbeddedCheckoutProvider = function EmbeddedCheckoutProvider(_ref) {\n  var rawStripeProp = _ref.stripe,\n      options = _ref.options,\n      children = _ref.children;\n  var parsed = React.useMemo(function () {\n    return parseStripeProp(rawStripeProp, INVALID_STRIPE_ERROR);\n  }, [rawStripeProp]);\n  var embeddedCheckoutPromise = React.useRef(null);\n  var loadedStripe = React.useRef(null);\n\n  var _React$useState = React.useState({\n    embeddedCheckout: null\n  }),\n      _React$useState2 = _slicedToArray(_React$useState, 2),\n      ctx = _React$useState2[0],\n      setContext = _React$useState2[1];\n\n  React.useEffect(function () {\n    // Don't support any ctx updates once embeddedCheckout or stripe is set.\n    if (loadedStripe.current || embeddedCheckoutPromise.current) {\n      return;\n    }\n\n    var setStripeAndInitEmbeddedCheckout = function setStripeAndInitEmbeddedCheckout(stripe) {\n      if (loadedStripe.current || embeddedCheckoutPromise.current) return;\n      loadedStripe.current = stripe;\n      embeddedCheckoutPromise.current = loadedStripe.current.initEmbeddedCheckout(options).then(function (embeddedCheckout) {\n        setContext({\n          embeddedCheckout: embeddedCheckout\n        });\n      });\n    }; // For an async stripePromise, store it once resolved\n\n\n    if (parsed.tag === 'async' && !loadedStripe.current && (options.clientSecret || options.fetchClientSecret)) {\n      parsed.stripePromise.then(function (stripe) {\n        if (stripe) {\n          setStripeAndInitEmbeddedCheckout(stripe);\n        }\n      });\n    } else if (parsed.tag === 'sync' && !loadedStripe.current && (options.clientSecret || options.fetchClientSecret)) {\n      // Or, handle a sync stripe instance going from null -> populated\n      setStripeAndInitEmbeddedCheckout(parsed.stripe);\n    }\n  }, [parsed, options, ctx, loadedStripe]);\n  React.useEffect(function () {\n    // cleanup on unmount\n    return function () {\n      // If embedded checkout is fully initialized, destroy it.\n      if (ctx.embeddedCheckout) {\n        embeddedCheckoutPromise.current = null;\n        ctx.embeddedCheckout.destroy();\n      } else if (embeddedCheckoutPromise.current) {\n        // If embedded checkout is still initializing, destroy it once\n        // it's done. This could be caused by unmounting very quickly\n        // after mounting.\n        embeddedCheckoutPromise.current.then(function () {\n          embeddedCheckoutPromise.current = null;\n\n          if (ctx.embeddedCheckout) {\n            ctx.embeddedCheckout.destroy();\n          }\n        });\n      }\n    };\n  }, [ctx.embeddedCheckout]); // Attach react-stripe-js version to stripe.js instance\n\n  React.useEffect(function () {\n    registerWithStripeJs(loadedStripe);\n  }, [loadedStripe]); // Warn on changes to stripe prop.\n  // The stripe prop value can only go from null to non-null once and\n  // can't be changed after that.\n\n  var prevStripe = usePrevious(rawStripeProp);\n  React.useEffect(function () {\n    if (prevStripe !== null && prevStripe !== rawStripeProp) {\n      console.warn('Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the `stripe` prop after setting it.');\n    }\n  }, [prevStripe, rawStripeProp]); // Warn on changes to options.\n\n  var prevOptions = usePrevious(options);\n  React.useEffect(function () {\n    if (prevOptions == null) {\n      return;\n    }\n\n    if (options == null) {\n      console.warn('Unsupported prop change on EmbeddedCheckoutProvider: You cannot unset options after setting them.');\n      return;\n    }\n\n    if (options.clientSecret === undefined && options.fetchClientSecret === undefined) {\n      console.warn('Invalid props passed to EmbeddedCheckoutProvider: You must provide one of either `options.fetchClientSecret` or `options.clientSecret`.');\n    }\n\n    if (prevOptions.clientSecret != null && options.clientSecret !== prevOptions.clientSecret) {\n      console.warn('Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the client secret after setting it. Unmount and create a new instance of EmbeddedCheckoutProvider instead.');\n    }\n\n    if (prevOptions.fetchClientSecret != null && options.fetchClientSecret !== prevOptions.fetchClientSecret) {\n      console.warn('Unsupported prop change on EmbeddedCheckoutProvider: You cannot change fetchClientSecret after setting it. Unmount and create a new instance of EmbeddedCheckoutProvider instead.');\n    }\n\n    if (prevOptions.onComplete != null && options.onComplete !== prevOptions.onComplete) {\n      console.warn('Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the onComplete option after setting it.');\n    }\n\n    if (prevOptions.onShippingDetailsChange != null && options.onShippingDetailsChange !== prevOptions.onShippingDetailsChange) {\n      console.warn('Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the onShippingDetailsChange option after setting it.');\n    }\n\n    if (prevOptions.onLineItemsChange != null && options.onLineItemsChange !== prevOptions.onLineItemsChange) {\n      console.warn('Unsupported prop change on EmbeddedCheckoutProvider: You cannot change the onLineItemsChange option after setting it.');\n    }\n  }, [prevOptions, options]);\n  return /*#__PURE__*/React.createElement(EmbeddedCheckoutContext.Provider, {\n    value: ctx\n  }, children);\n};\n\nvar EmbeddedCheckoutClientElement = function EmbeddedCheckoutClientElement(_ref) {\n  var id = _ref.id,\n      className = _ref.className;\n\n  var _useEmbeddedCheckoutC = useEmbeddedCheckoutContext(),\n      embeddedCheckout = _useEmbeddedCheckoutC.embeddedCheckout;\n\n  var isMounted = React.useRef(false);\n  var domNode = React.useRef(null);\n  React.useLayoutEffect(function () {\n    if (!isMounted.current && embeddedCheckout && domNode.current !== null) {\n      embeddedCheckout.mount(domNode.current);\n      isMounted.current = true;\n    } // Clean up on unmount\n\n\n    return function () {\n      if (isMounted.current && embeddedCheckout) {\n        try {\n          embeddedCheckout.unmount();\n          isMounted.current = false;\n        } catch (e) {// Do nothing.\n          // Parent effects are destroyed before child effects, so\n          // in cases where both the EmbeddedCheckoutProvider and\n          // the EmbeddedCheckout component are removed at the same\n          // time, the embeddedCheckout instance will be destroyed,\n          // which causes an error when calling unmount.\n        }\n      }\n    };\n  }, [embeddedCheckout]);\n  return /*#__PURE__*/React.createElement(\"div\", {\n    ref: domNode,\n    id: id,\n    className: className\n  });\n}; // Only render the wrapper in a server environment.\n\n\nvar EmbeddedCheckoutServerElement = function EmbeddedCheckoutServerElement(_ref2) {\n  var id = _ref2.id,\n      className = _ref2.className;\n  // Validate that we are in the right context by calling useEmbeddedCheckoutContext.\n  useEmbeddedCheckoutContext();\n  return /*#__PURE__*/React.createElement(\"div\", {\n    id: id,\n    className: className\n  });\n};\n\nvar EmbeddedCheckout = isServer ? EmbeddedCheckoutServerElement : EmbeddedCheckoutClientElement;\n\n/**\n * @docs https://stripe.com/docs/stripe-js/react#usestripe-hook\n */\n\nvar useStripe = function useStripe() {\n  var _useElementsOrCheckou = useElementsOrCheckoutSdkContextWithUseCase('calls useStripe()'),\n      stripe = _useElementsOrCheckou.stripe;\n\n  return stripe;\n};\n\n/**\n * Requires beta access:\n * Contact [Stripe support](https://support.stripe.com/) for more information.\n *\n * @docs https://stripe.com/docs/stripe-js/react#element-components\n */\n\nvar AuBankAccountElement = createElementComponent('auBankAccount', isServer);\n/**\n * @docs https://stripe.com/docs/stripe-js/react#element-components\n */\n\nvar CardElement = createElementComponent('card', isServer);\n/**\n * @docs https://stripe.com/docs/stripe-js/react#element-components\n */\n\nvar CardNumberElement = createElementComponent('cardNumber', isServer);\n/**\n * @docs https://stripe.com/docs/stripe-js/react#element-components\n */\n\nvar CardExpiryElement = createElementComponent('cardExpiry', isServer);\n/**\n * @docs https://stripe.com/docs/stripe-js/react#element-components\n */\n\nvar CardCvcElement = createElementComponent('cardCvc', isServer);\n/**\n * @docs https://stripe.com/docs/stripe-js/react#element-components\n */\n\nvar FpxBankElement = createElementComponent('fpxBank', isServer);\n/**\n * @docs https://stripe.com/docs/stripe-js/react#element-components\n */\n\nvar IbanElement = createElementComponent('iban', isServer);\n/**\n * @docs https://stripe.com/docs/stripe-js/react#element-components\n */\n\nvar IdealBankElement = createElementComponent('idealBank', isServer);\n/**\n * @docs https://stripe.com/docs/stripe-js/react#element-components\n */\n\nvar P24BankElement = createElementComponent('p24Bank', isServer);\n/**\n * @docs https://stripe.com/docs/stripe-js/react#element-components\n */\n\nvar EpsBankElement = createElementComponent('epsBank', isServer);\nvar PaymentElement = createElementComponent('payment', isServer);\n/**\n * @docs https://stripe.com/docs/stripe-js/react#element-components\n */\n\nvar ExpressCheckoutElement = createElementComponent('expressCheckout', isServer);\n/**\n * Requires beta access:\n * Contact [Stripe support](https://support.stripe.com/) for more information.\n */\n\nvar CurrencySelectorElement = createElementComponent('currencySelector', isServer);\n/**\n * @docs https://stripe.com/docs/stripe-js/react#element-components\n */\n\nvar PaymentRequestButtonElement = createElementComponent('paymentRequestButton', isServer);\n/**\n * @docs https://stripe.com/docs/stripe-js/react#element-components\n */\n\nvar LinkAuthenticationElement = createElementComponent('linkAuthentication', isServer);\n/**\n * @docs https://stripe.com/docs/stripe-js/react#element-components\n */\n\nvar AddressElement = createElementComponent('address', isServer);\n/**\n * @deprecated\n * Use `AddressElement` instead.\n *\n * @docs https://stripe.com/docs/stripe-js/react#element-components\n */\n\nvar ShippingAddressElement = createElementComponent('shippingAddress', isServer);\n/**\n * @docs https://stripe.com/docs/stripe-js/react#element-components\n */\n\nvar PaymentMethodMessagingElement = createElementComponent('paymentMethodMessaging', isServer);\n/**\n * @docs https://stripe.com/docs/stripe-js/react#element-components\n */\n\nvar AffirmMessageElement = createElementComponent('affirmMessage', isServer);\n/**\n * @docs https://stripe.com/docs/stripe-js/react#element-components\n */\n\nvar AfterpayClearpayMessageElement = createElementComponent('afterpayClearpayMessage', isServer);\n\nexport { AddressElement, AffirmMessageElement, AfterpayClearpayMessageElement, AuBankAccountElement, CardCvcElement, CardElement, CardExpiryElement, CardNumberElement, CheckoutProvider, CurrencySelectorElement, Elements, ElementsConsumer, EmbeddedCheckout, EmbeddedCheckoutProvider, EpsBankElement, ExpressCheckoutElement, FpxBankElement, IbanElement, IdealBankElement, LinkAuthenticationElement, P24BankElement, PaymentElement, PaymentMethodMessagingElement, PaymentRequestButtonElement, ShippingAddressElement, useCheckout, useElements, useStripe };\n"], "mappings": ";;;;;;;;;;;AAAA,mBAAkB;AAClB,wBAAsB;AAEtB,SAAS,QAAQ,QAAQ,gBAAgB;AACvC,MAAI,OAAO,OAAO,KAAK,MAAM;AAE7B,MAAI,OAAO,uBAAuB;AAChC,QAAI,UAAU,OAAO,sBAAsB,MAAM;AAEjD,QAAI,gBAAgB;AAClB,gBAAU,QAAQ,OAAO,SAAU,KAAK;AACtC,eAAO,OAAO,yBAAyB,QAAQ,GAAG,EAAE;AAAA,MACtD,CAAC;AAAA,IACH;AAEA,SAAK,KAAK,MAAM,MAAM,OAAO;AAAA,EAC/B;AAEA,SAAO;AACT;AAEA,SAAS,eAAe,QAAQ;AAC9B,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,QAAI,SAAS,UAAU,CAAC,KAAK,OAAO,UAAU,CAAC,IAAI,CAAC;AAEpD,QAAI,IAAI,GAAG;AACT,cAAQ,OAAO,MAAM,GAAG,IAAI,EAAE,QAAQ,SAAU,KAAK;AACnD,wBAAgB,QAAQ,KAAK,OAAO,GAAG,CAAC;AAAA,MAC1C,CAAC;AAAA,IACH,WAAW,OAAO,2BAA2B;AAC3C,aAAO,iBAAiB,QAAQ,OAAO,0BAA0B,MAAM,CAAC;AAAA,IAC1E,OAAO;AACL,cAAQ,OAAO,MAAM,CAAC,EAAE,QAAQ,SAAU,KAAK;AAC7C,eAAO,eAAe,QAAQ,KAAK,OAAO,yBAAyB,QAAQ,GAAG,CAAC;AAAA,MACjF,CAAC;AAAA,IACH;AAAA,EACF;AAEA,SAAO;AACT;AAEA,SAAS,QAAQ,KAAK;AACpB;AAEA,MAAI,OAAO,WAAW,cAAc,OAAO,OAAO,aAAa,UAAU;AACvE,cAAU,SAAUA,MAAK;AACvB,aAAO,OAAOA;AAAA,IAChB;AAAA,EACF,OAAO;AACL,cAAU,SAAUA,MAAK;AACvB,aAAOA,QAAO,OAAO,WAAW,cAAcA,KAAI,gBAAgB,UAAUA,SAAQ,OAAO,YAAY,WAAW,OAAOA;AAAA,IAC3H;AAAA,EACF;AAEA,SAAO,QAAQ,GAAG;AACpB;AAEA,SAAS,gBAAgB,KAAK,KAAK,OAAO;AACxC,MAAI,OAAO,KAAK;AACd,WAAO,eAAe,KAAK,KAAK;AAAA,MAC9B;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,OAAO;AACL,QAAI,GAAG,IAAI;AAAA,EACb;AAEA,SAAO;AACT;AAEA,SAAS,8BAA8B,QAAQ,UAAU;AACvD,MAAI,UAAU,KAAM,QAAO,CAAC;AAC5B,MAAI,SAAS,CAAC;AACd,MAAI,aAAa,OAAO,KAAK,MAAM;AACnC,MAAI,KAAK;AAET,OAAK,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AACtC,UAAM,WAAW,CAAC;AAClB,QAAI,SAAS,QAAQ,GAAG,KAAK,EAAG;AAChC,WAAO,GAAG,IAAI,OAAO,GAAG;AAAA,EAC1B;AAEA,SAAO;AACT;AAEA,SAAS,yBAAyB,QAAQ,UAAU;AAClD,MAAI,UAAU,KAAM,QAAO,CAAC;AAE5B,MAAI,SAAS,8BAA8B,QAAQ,QAAQ;AAE3D,MAAI,KAAK;AAET,MAAI,OAAO,uBAAuB;AAChC,QAAI,mBAAmB,OAAO,sBAAsB,MAAM;AAE1D,SAAK,IAAI,GAAG,IAAI,iBAAiB,QAAQ,KAAK;AAC5C,YAAM,iBAAiB,CAAC;AACxB,UAAI,SAAS,QAAQ,GAAG,KAAK,EAAG;AAChC,UAAI,CAAC,OAAO,UAAU,qBAAqB,KAAK,QAAQ,GAAG,EAAG;AAC9D,aAAO,GAAG,IAAI,OAAO,GAAG;AAAA,IAC1B;AAAA,EACF;AAEA,SAAO;AACT;AAEA,SAAS,eAAe,KAAK,GAAG;AAC9B,SAAO,gBAAgB,GAAG,KAAK,sBAAsB,KAAK,CAAC,KAAK,4BAA4B,KAAK,CAAC,KAAK,iBAAiB;AAC1H;AAEA,SAAS,gBAAgB,KAAK;AAC5B,MAAI,MAAM,QAAQ,GAAG,EAAG,QAAO;AACjC;AAEA,SAAS,sBAAsB,KAAK,GAAG;AACrC,MAAI,KAAK,QAAQ,OAAO,WAAW,eAAe,IAAI,OAAO,QAAQ,KAAK,IAAI,YAAY;AAE1F,MAAI,MAAM,KAAM;AAChB,MAAI,OAAO,CAAC;AACZ,MAAI,KAAK;AACT,MAAI,KAAK;AAET,MAAI,IAAI;AAER,MAAI;AACF,SAAK,KAAK,GAAG,KAAK,GAAG,GAAG,EAAE,MAAM,KAAK,GAAG,KAAK,GAAG,OAAO,KAAK,MAAM;AAChE,WAAK,KAAK,GAAG,KAAK;AAElB,UAAI,KAAK,KAAK,WAAW,EAAG;AAAA,IAC9B;AAAA,EACF,SAAS,KAAK;AACZ,SAAK;AACL,SAAK;AAAA,EACP,UAAE;AACA,QAAI;AACF,UAAI,CAAC,MAAM,GAAG,QAAQ,KAAK,KAAM,IAAG,QAAQ,EAAE;AAAA,IAChD,UAAE;AACA,UAAI,GAAI,OAAM;AAAA,IAChB;AAAA,EACF;AAEA,SAAO;AACT;AAEA,SAAS,4BAA4B,GAAG,QAAQ;AAC9C,MAAI,CAAC,EAAG;AACR,MAAI,OAAO,MAAM,SAAU,QAAO,kBAAkB,GAAG,MAAM;AAC7D,MAAI,IAAI,OAAO,UAAU,SAAS,KAAK,CAAC,EAAE,MAAM,GAAG,EAAE;AACrD,MAAI,MAAM,YAAY,EAAE,YAAa,KAAI,EAAE,YAAY;AACvD,MAAI,MAAM,SAAS,MAAM,MAAO,QAAO,MAAM,KAAK,CAAC;AACnD,MAAI,MAAM,eAAe,2CAA2C,KAAK,CAAC,EAAG,QAAO,kBAAkB,GAAG,MAAM;AACjH;AAEA,SAAS,kBAAkB,KAAK,KAAK;AACnC,MAAI,OAAO,QAAQ,MAAM,IAAI,OAAQ,OAAM,IAAI;AAE/C,WAAS,IAAI,GAAG,OAAO,IAAI,MAAM,GAAG,GAAG,IAAI,KAAK,IAAK,MAAK,CAAC,IAAI,IAAI,CAAC;AAEpE,SAAO;AACT;AAEA,SAAS,mBAAmB;AAC1B,QAAM,IAAI,UAAU,2IAA2I;AACjK;AAEA,IAAI,iBAAiB,SAASC,gBAAe,SAAS,OAAO,IAAI;AAC/D,MAAI,YAAY,CAAC,CAAC;AAClB,MAAI,QAAQ,aAAAC,QAAM,OAAO,EAAE;AAG3B,eAAAA,QAAM,UAAU,WAAY;AAC1B,UAAM,UAAU;AAAA,EAClB,GAAG,CAAC,EAAE,CAAC;AACP,eAAAA,QAAM,UAAU,WAAY;AAC1B,QAAI,CAAC,aAAa,CAAC,SAAS;AAC1B,aAAO,WAAY;AAAA,MAAC;AAAA,IACtB;AAEA,QAAI,cAAc,SAASC,eAAc;AACvC,UAAI,MAAM,SAAS;AACjB,cAAM,QAAQ,MAAM,OAAO,SAAS;AAAA,MACtC;AAAA,IACF;AAEA,YAAQ,GAAG,OAAO,WAAW;AAC7B,WAAO,WAAY;AACjB,cAAQ,IAAI,OAAO,WAAW;AAAA,IAChC;AAAA,EACF,GAAG,CAAC,WAAW,OAAO,SAAS,KAAK,CAAC;AACvC;AAEA,IAAI,cAAc,SAASC,aAAY,OAAO;AAC5C,MAAI,MAAM,aAAAF,QAAM,OAAO,KAAK;AAC5B,eAAAA,QAAM,UAAU,WAAY;AAC1B,QAAI,UAAU;AAAA,EAChB,GAAG,CAAC,KAAK,CAAC;AACV,SAAO,IAAI;AACb;AAEA,IAAI,kBAAkB,SAASG,iBAAgB,KAAK;AAClD,SAAO,QAAQ,QAAQ,QAAQ,GAAG,MAAM;AAC1C;AACA,IAAI,YAAY,SAASC,WAAU,KAAK;AACtC,SAAO,gBAAgB,GAAG,KAAK,OAAO,IAAI,SAAS;AACrD;AAIA,IAAI,WAAW,SAASC,UAAS,KAAK;AACpC,SAAO,gBAAgB,GAAG,KAAK,OAAO,IAAI,aAAa,cAAc,OAAO,IAAI,gBAAgB,cAAc,OAAO,IAAI,wBAAwB,cAAc,OAAO,IAAI,uBAAuB;AACnM;AAEA,IAAI,mBAAmB;AACvB,IAAI,UAAU,SAASC,SAAQ,MAAM,OAAO;AAC1C,MAAI,CAAC,gBAAgB,IAAI,KAAK,CAAC,gBAAgB,KAAK,GAAG;AACrD,WAAO,SAAS;AAAA,EAClB;AAEA,MAAI,YAAY,MAAM,QAAQ,IAAI;AAClC,MAAI,aAAa,MAAM,QAAQ,KAAK;AACpC,MAAI,cAAc,WAAY,QAAO;AACrC,MAAI,kBAAkB,OAAO,UAAU,SAAS,KAAK,IAAI,MAAM;AAC/D,MAAI,mBAAmB,OAAO,UAAU,SAAS,KAAK,KAAK,MAAM;AACjE,MAAI,oBAAoB,iBAAkB,QAAO;AAGjD,MAAI,CAAC,mBAAmB,CAAC,UAAW,QAAO,SAAS;AACpD,MAAI,WAAW,OAAO,KAAK,IAAI;AAC/B,MAAI,YAAY,OAAO,KAAK,KAAK;AACjC,MAAI,SAAS,WAAW,UAAU,OAAQ,QAAO;AACjD,MAAI,SAAS,CAAC;AAEd,WAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK,GAAG;AAC3C,WAAO,SAAS,CAAC,CAAC,IAAI;AAAA,EACxB;AAEA,WAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM,GAAG;AAC/C,WAAO,UAAU,EAAE,CAAC,IAAI;AAAA,EAC1B;AAEA,MAAI,UAAU,OAAO,KAAK,MAAM;AAEhC,MAAI,QAAQ,WAAW,SAAS,QAAQ;AACtC,WAAO;AAAA,EACT;AAEA,MAAI,IAAI;AACR,MAAI,IAAI;AAER,MAAI,OAAO,SAASC,MAAK,KAAK;AAC5B,WAAOD,SAAQ,EAAE,GAAG,GAAG,EAAE,GAAG,CAAC;AAAA,EAC/B;AAEA,SAAO,QAAQ,MAAM,IAAI;AAC3B;AAEA,IAAI,+BAA+B,SAASE,8BAA6B,SAAS,aAAa,eAAe;AAC5G,MAAI,CAAC,gBAAgB,OAAO,GAAG;AAC7B,WAAO;AAAA,EACT;AAEA,SAAO,OAAO,KAAK,OAAO,EAAE,OAAO,SAAU,YAAY,KAAK;AAC5D,QAAI,YAAY,CAAC,gBAAgB,WAAW,KAAK,CAAC,QAAQ,QAAQ,GAAG,GAAG,YAAY,GAAG,CAAC;AAExF,QAAI,cAAc,SAAS,GAAG,GAAG;AAC/B,UAAI,WAAW;AACb,gBAAQ,KAAK,oCAAoC,OAAO,KAAK,6BAA6B,CAAC;AAAA,MAC7F;AAEA,aAAO;AAAA,IACT;AAEA,QAAI,CAAC,WAAW;AACd,aAAO;AAAA,IACT;AAEA,WAAO,eAAe,eAAe,CAAC,GAAG,cAAc,CAAC,CAAC,GAAG,CAAC,GAAG,gBAAgB,CAAC,GAAG,KAAK,QAAQ,GAAG,CAAC,CAAC;AAAA,EACxG,GAAG,IAAI;AACT;AAEA,IAAI,yBAAyB;AAI7B,IAAI,iBAAiB,SAASC,gBAAe,aAAa;AACxD,MAAI,WAAW,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAEnF,MAAI,gBAAgB,QAAQ,SAAS,WAAW,GAAG;AACjD,WAAO;AAAA,EACT;AAEA,QAAM,IAAI,MAAM,QAAQ;AAC1B;AAEA,IAAI,kBAAkB,SAASC,iBAAgB,KAAK;AAClD,MAAI,WAAW,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAEnF,MAAI,UAAU,GAAG,GAAG;AAClB,WAAO;AAAA,MACL,KAAK;AAAA,MACL,eAAe,QAAQ,QAAQ,GAAG,EAAE,KAAK,SAAU,QAAQ;AACzD,eAAO,eAAe,QAAQ,QAAQ;AAAA,MACxC,CAAC;AAAA,IACH;AAAA,EACF;AAEA,MAAI,SAAS,eAAe,KAAK,QAAQ;AAEzC,MAAI,WAAW,MAAM;AACnB,WAAO;AAAA,MACL,KAAK;AAAA,IACP;AAAA,EACF;AAEA,SAAO;AAAA,IACL,KAAK;AAAA,IACL;AAAA,EACF;AACF;AAEA,IAAI,uBAAuB,SAASC,sBAAqB,QAAQ;AAC/D,MAAI,CAAC,UAAU,CAAC,OAAO,oBAAoB,CAAC,OAAO,iBAAiB;AAClE;AAAA,EACF;AAEA,SAAO,iBAAiB;AAAA,IACtB,MAAM;AAAA,IACN,SAAS;AAAA,EACX,CAAC;AAED,SAAO,gBAAgB;AAAA,IACrB,MAAM;AAAA,IACN,SAAS;AAAA,IACT,KAAK;AAAA,EACP,CAAC;AACH;AAEA,IAAI,kBAA+B,aAAAX,QAAM,cAAc,IAAI;AAC3D,gBAAgB,cAAc;AAC9B,IAAI,uBAAuB,SAASY,sBAAqB,KAAK,SAAS;AACrE,MAAI,CAAC,KAAK;AACR,UAAM,IAAI,MAAM,+EAA+E,OAAO,SAAS,6BAA6B,CAAC;AAAA,EAC/I;AAEA,SAAO;AACT;AAYA,IAAI,WAAW,SAASC,UAAS,MAAM;AACrC,MAAI,gBAAgB,KAAK,QACrB,UAAU,KAAK,SACf,WAAW,KAAK;AACpB,MAAI,SAAS,aAAAb,QAAM,QAAQ,WAAY;AACrC,WAAO,gBAAgB,aAAa;AAAA,EACtC,GAAG,CAAC,aAAa,CAAC;AAElB,MAAI,kBAAkB,aAAAA,QAAM,SAAS,WAAY;AAC/C,WAAO;AAAA,MACL,QAAQ,OAAO,QAAQ,SAAS,OAAO,SAAS;AAAA,MAChD,UAAU,OAAO,QAAQ,SAAS,OAAO,OAAO,SAAS,OAAO,IAAI;AAAA,IACtE;AAAA,EACF,CAAC,GACG,mBAAmB,eAAe,iBAAiB,CAAC,GACpD,MAAM,iBAAiB,CAAC,GACxB,aAAa,iBAAiB,CAAC;AAEnC,eAAAA,QAAM,UAAU,WAAY;AAC1B,QAAI,YAAY;AAEhB,QAAI,iBAAiB,SAASc,gBAAe,QAAQ;AACnD,iBAAW,SAAUC,MAAK;AAExB,YAAIA,KAAI,OAAQ,QAAOA;AACvB,eAAO;AAAA,UACL;AAAA,UACA,UAAU,OAAO,SAAS,OAAO;AAAA,QACnC;AAAA,MACF,CAAC;AAAA,IACH;AAGA,QAAI,OAAO,QAAQ,WAAW,CAAC,IAAI,QAAQ;AACzC,aAAO,cAAc,KAAK,SAAU,QAAQ;AAC1C,YAAI,UAAU,WAAW;AAIvB,yBAAe,MAAM;AAAA,QACvB;AAAA,MACF,CAAC;AAAA,IACH,WAAW,OAAO,QAAQ,UAAU,CAAC,IAAI,QAAQ;AAE/C,qBAAe,OAAO,MAAM;AAAA,IAC9B;AAEA,WAAO,WAAY;AACjB,kBAAY;AAAA,IACd;AAAA,EACF,GAAG,CAAC,QAAQ,KAAK,OAAO,CAAC;AAEzB,MAAI,aAAa,YAAY,aAAa;AAC1C,eAAAf,QAAM,UAAU,WAAY;AAC1B,QAAI,eAAe,QAAQ,eAAe,eAAe;AACvD,cAAQ,KAAK,4FAA4F;AAAA,IAC3G;AAAA,EACF,GAAG,CAAC,YAAY,aAAa,CAAC;AAE9B,MAAI,cAAc,YAAY,OAAO;AACrC,eAAAA,QAAM,UAAU,WAAY;AAC1B,QAAI,CAAC,IAAI,UAAU;AACjB;AAAA,IACF;AAEA,QAAI,UAAU,6BAA6B,SAAS,aAAa,CAAC,gBAAgB,OAAO,CAAC;AAE1F,QAAI,SAAS;AACX,UAAI,SAAS,OAAO,OAAO;AAAA,IAC7B;AAAA,EACF,GAAG,CAAC,SAAS,aAAa,IAAI,QAAQ,CAAC;AAEvC,eAAAA,QAAM,UAAU,WAAY;AAC1B,yBAAqB,IAAI,MAAM;AAAA,EACjC,GAAG,CAAC,IAAI,MAAM,CAAC;AACf,SAAoB,aAAAA,QAAM,cAAc,gBAAgB,UAAU;AAAA,IAChE,OAAO;AAAA,EACT,GAAG,QAAQ;AACb;AACA,SAAS,YAAY;AAAA,EACnB,QAAQ,kBAAAgB,QAAU;AAAA,EAClB,SAAS,kBAAAA,QAAU;AACrB;AACA,IAAI,gCAAgC,SAASC,+BAA8B,gBAAgB;AACzF,MAAI,MAAM,aAAAjB,QAAM,WAAW,eAAe;AAC1C,SAAO,qBAAqB,KAAK,cAAc;AACjD;AAKA,IAAI,cAAc,SAASkB,eAAc;AACvC,MAAI,wBAAwB,8BAA8B,qBAAqB,GAC3E,WAAW,sBAAsB;AAErC,SAAO;AACT;AAKA,IAAI,mBAAmB,SAASC,kBAAiB,OAAO;AACtD,MAAI,WAAW,MAAM;AACrB,MAAI,MAAM,8BAA8B,2BAA2B;AAEnE,SAAO,SAAS,GAAG;AACrB;AACA,iBAAiB,YAAY;AAAA,EAC3B,UAAU,kBAAAH,QAAU,KAAK;AAC3B;AAEA,IAAI,cAAc,CAAC,MAAM,SAAS;AAClC,IAAI,qBAAkC,aAAAhB,QAAM,cAAc,IAAI;AAC9D,mBAAmB,cAAc;AACjC,IAAI,0BAA0B,SAASoB,yBAAwB,KAAK,SAAS;AAC3E,MAAI,CAAC,KAAK;AACR,UAAM,IAAI,MAAM,uFAAuF,OAAO,SAAS,qCAAqC,CAAC;AAAA,EAC/J;AAEA,SAAO;AACT;AACA,IAAI,kBAA+B,aAAApB,QAAM,cAAc,IAAI;AAC3D,gBAAgB,cAAc;AAC9B,IAAI,8BAA8B,SAASqB,6BAA4B,aAAa,cAAc;AAChG,MAAI,CAAC,aAAa;AAChB,WAAO;AAAA,EACT;AAEA,cAAY;AACR,cAAY;AACZ,MAAI,UAAU,yBAAyB,aAAa,WAAW;AAEnE,MAAI,CAAC,cAAc;AACjB,WAAO,OAAO,OAAO,YAAY,QAAQ,GAAG,OAAO;AAAA,EACrD;AAEA,SAAO,OAAO,OAAO,cAAc,OAAO;AAC5C;AACA,IAAI,yBAAyB;AAC7B,IAAI,mBAAmB,SAASC,kBAAiB,MAAM;AACrD,MAAI,gBAAgB,KAAK,QACrB,UAAU,KAAK,SACf,WAAW,KAAK;AACpB,MAAI,SAAS,aAAAtB,QAAM,QAAQ,WAAY;AACrC,WAAO,gBAAgB,eAAe,sBAAsB;AAAA,EAC9D,GAAG,CAAC,aAAa,CAAC;AAElB,MAAI,kBAAkB,aAAAA,QAAM,SAAS,IAAI,GACrC,mBAAmB,eAAe,iBAAiB,CAAC,GACpD,UAAU,iBAAiB,CAAC,GAC5B,aAAa,iBAAiB,CAAC;AAEnC,MAAI,mBAAmB,aAAAA,QAAM,SAAS,WAAY;AAChD,WAAO;AAAA,MACL,QAAQ,OAAO,QAAQ,SAAS,OAAO,SAAS;AAAA,MAChD,aAAa;AAAA,IACf;AAAA,EACF,CAAC,GACG,mBAAmB,eAAe,kBAAkB,CAAC,GACrD,MAAM,iBAAiB,CAAC,GACxB,aAAa,iBAAiB,CAAC;AAEnC,MAAI,iBAAiB,SAASc,gBAAe,QAAQ,aAAa;AAChE,eAAW,SAAUC,MAAK;AACxB,UAAIA,KAAI,UAAUA,KAAI,aAAa;AACjC,eAAOA;AAAA,MACT;AAEA,aAAO;AAAA,QACL;AAAA,QACA;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AAGA,MAAI,wBAAwB,aAAAf,QAAM,OAAO,KAAK;AAC9C,eAAAA,QAAM,UAAU,WAAY;AAC1B,QAAI,YAAY;AAEhB,QAAI,OAAO,QAAQ,WAAW,CAAC,IAAI,QAAQ;AACzC,aAAO,cAAc,KAAK,SAAU,QAAQ;AAC1C,YAAI,UAAU,aAAa,CAAC,sBAAsB,SAAS;AAIzD,gCAAsB,UAAU;AAChC,iBAAO,aAAa,OAAO,EAAE,KAAK,SAAU,aAAa;AACvD,gBAAI,aAAa;AACf,6BAAe,QAAQ,WAAW;AAClC,0BAAY,GAAG,UAAU,UAAU;AAAA,YACrC;AAAA,UACF,CAAC;AAAA,QACH;AAAA,MACF,CAAC;AAAA,IACH,WAAW,OAAO,QAAQ,UAAU,OAAO,UAAU,CAAC,sBAAsB,SAAS;AACnF,4BAAsB,UAAU;AAChC,aAAO,OAAO,aAAa,OAAO,EAAE,KAAK,SAAU,aAAa;AAC9D,YAAI,aAAa;AACf,yBAAe,OAAO,QAAQ,WAAW;AACzC,sBAAY,GAAG,UAAU,UAAU;AAAA,QACrC;AAAA,MACF,CAAC;AAAA,IACH;AAEA,WAAO,WAAY;AACjB,kBAAY;AAAA,IACd;AAAA,EACF,GAAG,CAAC,QAAQ,KAAK,SAAS,UAAU,CAAC;AAErC,MAAI,aAAa,YAAY,aAAa;AAC1C,eAAAA,QAAM,UAAU,WAAY;AAC1B,QAAI,eAAe,QAAQ,eAAe,eAAe;AACvD,cAAQ,KAAK,oGAAoG;AAAA,IACnH;AAAA,EACF,GAAG,CAAC,YAAY,aAAa,CAAC;AAE9B,MAAI,cAAc,YAAY,OAAO;AACrC,MAAI,kBAAkB,YAAY,IAAI,WAAW;AACjD,eAAAA,QAAM,UAAU,WAAY;AAC1B,QAAI,uBAAuB;AAG3B,QAAI,CAAC,IAAI,aAAa;AACpB;AAAA,IACF;AAEA,QAAI,qBAAqB,gBAAgB,QAAQ,gBAAgB,SAAS,UAAU,wBAAwB,YAAY,qBAAqB,QAAQ,0BAA0B,SAAS,SAAS,sBAAsB;AACvN,QAAI,oBAAoB,YAAY,QAAQ,YAAY,SAAS,UAAU,wBAAwB,QAAQ,qBAAqB,QAAQ,0BAA0B,SAAS,SAAS,sBAAsB;AAC1M,QAAI,uBAAuB,CAAC,QAAQ,mBAAmB,kBAAkB;AACzE,QAAI,eAAe,CAAC,mBAAmB,IAAI;AAE3C,QAAI,sBAAsB,wBAAwB,eAAe;AAC/D,UAAI,YAAY,iBAAiB,iBAAiB;AAAA,IACpD;AAAA,EACF,GAAG,CAAC,SAAS,aAAa,IAAI,aAAa,eAAe,CAAC;AAE3D,eAAAA,QAAM,UAAU,WAAY;AAC1B,yBAAqB,IAAI,MAAM;AAAA,EACjC,GAAG,CAAC,IAAI,MAAM,CAAC;AACf,MAAI,uBAAuB,aAAAA,QAAM,QAAQ,WAAY;AACnD,WAAO,4BAA4B,IAAI,aAAa,OAAO;AAAA,EAC7D,GAAG,CAAC,IAAI,aAAa,OAAO,CAAC;AAE7B,MAAI,CAAC,IAAI,aAAa;AACpB,WAAO;AAAA,EACT;AAEA,SAAoB,aAAAA,QAAM,cAAc,mBAAmB,UAAU;AAAA,IACnE,OAAO;AAAA,EACT,GAAgB,aAAAA,QAAM,cAAc,gBAAgB,UAAU;AAAA,IAC5D,OAAO;AAAA,EACT,GAAG,QAAQ,CAAC;AACd;AACA,iBAAiB,YAAY;AAAA,EAC3B,QAAQ,kBAAAgB,QAAU;AAAA,EAClB,SAAS,kBAAAA,QAAU,MAAM;AAAA,IACvB,mBAAmB,kBAAAA,QAAU,KAAK;AAAA,IAClC,iBAAiB,kBAAAA,QAAU;AAAA,EAC7B,CAAC,EAAE;AACL;AACA,IAAI,mCAAmC,SAASO,kCAAiC,eAAe;AAC9F,MAAI,MAAM,aAAAvB,QAAM,WAAW,kBAAkB;AAC7C,SAAO,wBAAwB,KAAK,aAAa;AACnD;AACA,IAAI,6CAA6C,SAASwB,4CAA2C,eAAe;AAClH,MAAI,qBAAqB,aAAAxB,QAAM,WAAW,kBAAkB;AAC5D,MAAI,kBAAkB,aAAAA,QAAM,WAAW,eAAe;AAEtD,MAAI,sBAAsB,iBAAiB;AACzC,UAAM,IAAI,MAAM,6CAA6C,OAAO,eAAe,uDAAuD,CAAC;AAAA,EAC7I;AAEA,MAAI,oBAAoB;AACtB,WAAO,wBAAwB,oBAAoB,aAAa;AAAA,EAClE;AAEA,SAAO,qBAAqB,iBAAiB,aAAa;AAC5D;AACA,IAAI,cAAc,SAASyB,eAAc;AAEvC,mCAAiC,qBAAqB;AACtD,MAAI,MAAM,aAAAzB,QAAM,WAAW,eAAe;AAE1C,MAAI,CAAC,KAAK;AACR,UAAM,IAAI,MAAM,oIAAoI;AAAA,EACtJ;AAEA,SAAO;AACT;AAEA,IAAI,YAAY,CAAC,MAAM;AAEvB,IAAI,cAAc,SAAS0B,aAAY,KAAK;AAC1C,SAAO,IAAI,OAAO,CAAC,EAAE,YAAY,IAAI,IAAI,MAAM,CAAC;AAClD;AAEA,IAAI,yBAAyB,SAASC,wBAAuB,MAAMC,WAAU;AAC3E,MAAI,cAAc,GAAG,OAAO,YAAY,IAAI,GAAG,SAAS;AAExD,MAAI,gBAAgB,SAASC,eAAc,MAAM;AAC/C,QAAI,KAAK,KAAK,IACV,YAAY,KAAK,WACjB,eAAe,KAAK,SACpB,UAAU,iBAAiB,SAAS,CAAC,IAAI,cACzC,SAAS,KAAK,QACd,UAAU,KAAK,SACf,UAAU,KAAK,SACf,WAAW,KAAK,UAChB,WAAW,KAAK,UAChB,UAAU,KAAK,SACf,cAAc,KAAK,aACnB,gBAAgB,KAAK,eACrB,mBAAmB,KAAK,kBACxB,YAAY,KAAK,WACjB,WAAW,KAAK,UAChB,0BAA0B,KAAK,yBAC/B,uBAAuB,KAAK;AAChC,QAAI,MAAM,2CAA2C,WAAW,OAAO,aAAa,GAAG,CAAC;AACxF,QAAI,WAAW,cAAc,MAAM,IAAI,WAAW;AAClD,QAAI,cAAc,iBAAiB,MAAM,IAAI,cAAc;AAE3D,QAAI,kBAAkB,aAAA7B,QAAM,SAAS,IAAI,GACrC,mBAAmB,eAAe,iBAAiB,CAAC,GACpD,UAAU,iBAAiB,CAAC,GAC5B,aAAa,iBAAiB,CAAC;AAEnC,QAAI,aAAa,aAAAA,QAAM,OAAO,IAAI;AAClC,QAAI,UAAU,aAAAA,QAAM,OAAO,IAAI;AAI/B,mBAAe,SAAS,QAAQ,MAAM;AACtC,mBAAe,SAAS,SAAS,OAAO;AACxC,mBAAe,SAAS,UAAU,QAAQ;AAC1C,mBAAe,SAAS,SAAS,OAAO;AACxC,mBAAe,SAAS,aAAa,WAAW;AAChD,mBAAe,SAAS,eAAe,aAAa;AACpD,mBAAe,SAAS,kBAAkB,gBAAgB;AAC1D,mBAAe,SAAS,WAAW,SAAS;AAC5C,mBAAe,SAAS,UAAU,QAAQ;AAC1C,mBAAe,SAAS,yBAAyB,uBAAuB;AACxE,mBAAe,SAAS,sBAAsB,oBAAoB;AAClE,mBAAe,SAAS,UAAU,QAAQ;AAC1C,QAAI;AAEJ,QAAI,SAAS;AACX,UAAI,SAAS,mBAAmB;AAE9B,wBAAgB;AAAA,MAClB,OAAO;AAEL,wBAAgB,SAAS8B,iBAAgB;AACvC,kBAAQ,OAAO;AAAA,QACjB;AAAA,MACF;AAAA,IACF;AAEA,mBAAe,SAAS,SAAS,aAAa;AAC9C,iBAAA9B,QAAM,gBAAgB,WAAY;AAChC,UAAI,WAAW,YAAY,QAAQ,QAAQ,YAAY,SAAS,YAAY,cAAc;AACxF,YAAI,aAAa;AAEjB,YAAI,aAAa;AACf,kBAAQ,MAAM;AAAA,YACZ,KAAK;AACH,2BAAa,YAAY,qBAAqB,OAAO;AACrD;AAAA,YAEF,KAAK;AACH,kBAAI,UAAU,SAAS;AACrB,oBAAI,OAAO,QAAQ,MACf,cAAc,yBAAyB,SAAS,SAAS;AAE7D,oBAAI,SAAS,YAAY;AACvB,+BAAa,YAAY,6BAA6B,WAAW;AAAA,gBACnE,WAAW,SAAS,WAAW;AAC7B,+BAAa,YAAY,4BAA4B,WAAW;AAAA,gBAClE,OAAO;AACL,wBAAM,IAAI,MAAM,6DAA6D;AAAA,gBAC/E;AAAA,cACF,OAAO;AACL,sBAAM,IAAI,MAAM,qEAAqE;AAAA,cACvF;AAEA;AAAA,YAEF,KAAK;AACH,2BAAa,YAAY,6BAA6B,OAAO;AAC7D;AAAA,YAEF,KAAK;AACH,2BAAa,YAAY,8BAA8B;AACvD;AAAA,YAEF;AACE,oBAAM,IAAI,MAAM,wBAAwB,OAAO,aAAa,+KAA+K,CAAC;AAAA,UAChP;AAAA,QACF,WAAW,UAAU;AACnB,uBAAa,SAAS,OAAO,MAAM,OAAO;AAAA,QAC5C;AAGA,mBAAW,UAAU;AAErB,mBAAW,UAAU;AAErB,YAAI,YAAY;AACd,qBAAW,MAAM,QAAQ,OAAO;AAAA,QAClC;AAAA,MACF;AAAA,IACF,GAAG,CAAC,UAAU,aAAa,OAAO,CAAC;AACnC,QAAI,cAAc,YAAY,OAAO;AACrC,iBAAAA,QAAM,UAAU,WAAY;AAC1B,UAAI,CAAC,WAAW,SAAS;AACvB;AAAA,MACF;AAEA,UAAI,UAAU,6BAA6B,SAAS,aAAa,CAAC,gBAAgB,CAAC;AAEnF,UAAI,WAAW,YAAY,WAAW,SAAS;AAC7C,mBAAW,QAAQ,OAAO,OAAO;AAAA,MACnC;AAAA,IACF,GAAG,CAAC,SAAS,WAAW,CAAC;AACzB,iBAAAA,QAAM,gBAAgB,WAAY;AAChC,aAAO,WAAY;AACjB,YAAI,WAAW,WAAW,OAAO,WAAW,QAAQ,YAAY,YAAY;AAC1E,cAAI;AACF,uBAAW,QAAQ,QAAQ;AAC3B,uBAAW,UAAU;AAAA,UACvB,SAAS,OAAO;AAAA,UAChB;AAAA,QACF;AAAA,MACF;AAAA,IACF,GAAG,CAAC,CAAC;AACL,WAAoB,aAAAA,QAAM,cAAc,OAAO;AAAA,MAC7C;AAAA,MACA;AAAA,MACA,KAAK;AAAA,IACP,CAAC;AAAA,EACH;AAGA,MAAI,gBAAgB,SAAS+B,eAAc,OAAO;AAChD,+CAA2C,WAAW,OAAO,aAAa,GAAG,CAAC;AAC9E,QAAI,KAAK,MAAM,IACX,YAAY,MAAM;AACtB,WAAoB,aAAA/B,QAAM,cAAc,OAAO;AAAA,MAC7C;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH;AAEA,MAAI,UAAU4B,YAAW,gBAAgB;AACzC,UAAQ,YAAY;AAAA,IAClB,IAAI,kBAAAZ,QAAU;AAAA,IACd,WAAW,kBAAAA,QAAU;AAAA,IACrB,UAAU,kBAAAA,QAAU;AAAA,IACpB,QAAQ,kBAAAA,QAAU;AAAA,IAClB,SAAS,kBAAAA,QAAU;AAAA,IACnB,SAAS,kBAAAA,QAAU;AAAA,IACnB,UAAU,kBAAAA,QAAU;AAAA,IACpB,SAAS,kBAAAA,QAAU;AAAA,IACnB,aAAa,kBAAAA,QAAU;AAAA,IACvB,eAAe,kBAAAA,QAAU;AAAA,IACzB,kBAAkB,kBAAAA,QAAU;AAAA,IAC5B,WAAW,kBAAAA,QAAU;AAAA,IACrB,UAAU,kBAAAA,QAAU;AAAA,IACpB,yBAAyB,kBAAAA,QAAU;AAAA,IACnC,sBAAsB,kBAAAA,QAAU;AAAA,IAChC,SAAS,kBAAAA,QAAU;AAAA,EACrB;AACA,UAAQ,cAAc;AACtB,UAAQ,gBAAgB;AACxB,SAAO;AACT;AAEA,IAAI,WAAW,OAAO,WAAW;AAEjC,IAAI,0BAAuC,aAAAhB,QAAM,cAAc,IAAI;AACnE,wBAAwB,cAAc;AACtC,IAAI,6BAA6B,SAASgC,8BAA6B;AACrE,MAAI,MAAM,aAAAhC,QAAM,WAAW,uBAAuB;AAElD,MAAI,CAAC,KAAK;AACR,UAAM,IAAI,MAAM,mEAAmE;AAAA,EACrF;AAEA,SAAO;AACT;AACA,IAAI,uBAAuB;AAC3B,IAAI,2BAA2B,SAASiC,0BAAyB,MAAM;AACrE,MAAI,gBAAgB,KAAK,QACrB,UAAU,KAAK,SACf,WAAW,KAAK;AACpB,MAAI,SAAS,aAAAjC,QAAM,QAAQ,WAAY;AACrC,WAAO,gBAAgB,eAAe,oBAAoB;AAAA,EAC5D,GAAG,CAAC,aAAa,CAAC;AAClB,MAAI,0BAA0B,aAAAA,QAAM,OAAO,IAAI;AAC/C,MAAI,eAAe,aAAAA,QAAM,OAAO,IAAI;AAEpC,MAAI,kBAAkB,aAAAA,QAAM,SAAS;AAAA,IACnC,kBAAkB;AAAA,EACpB,CAAC,GACG,mBAAmB,eAAe,iBAAiB,CAAC,GACpD,MAAM,iBAAiB,CAAC,GACxB,aAAa,iBAAiB,CAAC;AAEnC,eAAAA,QAAM,UAAU,WAAY;AAE1B,QAAI,aAAa,WAAW,wBAAwB,SAAS;AAC3D;AAAA,IACF;AAEA,QAAI,mCAAmC,SAASkC,kCAAiC,QAAQ;AACvF,UAAI,aAAa,WAAW,wBAAwB,QAAS;AAC7D,mBAAa,UAAU;AACvB,8BAAwB,UAAU,aAAa,QAAQ,qBAAqB,OAAO,EAAE,KAAK,SAAU,kBAAkB;AACpH,mBAAW;AAAA,UACT;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AAGA,QAAI,OAAO,QAAQ,WAAW,CAAC,aAAa,YAAY,QAAQ,gBAAgB,QAAQ,oBAAoB;AAC1G,aAAO,cAAc,KAAK,SAAU,QAAQ;AAC1C,YAAI,QAAQ;AACV,2CAAiC,MAAM;AAAA,QACzC;AAAA,MACF,CAAC;AAAA,IACH,WAAW,OAAO,QAAQ,UAAU,CAAC,aAAa,YAAY,QAAQ,gBAAgB,QAAQ,oBAAoB;AAEhH,uCAAiC,OAAO,MAAM;AAAA,IAChD;AAAA,EACF,GAAG,CAAC,QAAQ,SAAS,KAAK,YAAY,CAAC;AACvC,eAAAlC,QAAM,UAAU,WAAY;AAE1B,WAAO,WAAY;AAEjB,UAAI,IAAI,kBAAkB;AACxB,gCAAwB,UAAU;AAClC,YAAI,iBAAiB,QAAQ;AAAA,MAC/B,WAAW,wBAAwB,SAAS;AAI1C,gCAAwB,QAAQ,KAAK,WAAY;AAC/C,kCAAwB,UAAU;AAElC,cAAI,IAAI,kBAAkB;AACxB,gBAAI,iBAAiB,QAAQ;AAAA,UAC/B;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF,GAAG,CAAC,IAAI,gBAAgB,CAAC;AAEzB,eAAAA,QAAM,UAAU,WAAY;AAC1B,yBAAqB,YAAY;AAAA,EACnC,GAAG,CAAC,YAAY,CAAC;AAIjB,MAAI,aAAa,YAAY,aAAa;AAC1C,eAAAA,QAAM,UAAU,WAAY;AAC1B,QAAI,eAAe,QAAQ,eAAe,eAAe;AACvD,cAAQ,KAAK,4GAA4G;AAAA,IAC3H;AAAA,EACF,GAAG,CAAC,YAAY,aAAa,CAAC;AAE9B,MAAI,cAAc,YAAY,OAAO;AACrC,eAAAA,QAAM,UAAU,WAAY;AAC1B,QAAI,eAAe,MAAM;AACvB;AAAA,IACF;AAEA,QAAI,WAAW,MAAM;AACnB,cAAQ,KAAK,mGAAmG;AAChH;AAAA,IACF;AAEA,QAAI,QAAQ,iBAAiB,UAAa,QAAQ,sBAAsB,QAAW;AACjF,cAAQ,KAAK,yIAAyI;AAAA,IACxJ;AAEA,QAAI,YAAY,gBAAgB,QAAQ,QAAQ,iBAAiB,YAAY,cAAc;AACzF,cAAQ,KAAK,mLAAmL;AAAA,IAClM;AAEA,QAAI,YAAY,qBAAqB,QAAQ,QAAQ,sBAAsB,YAAY,mBAAmB;AACxG,cAAQ,KAAK,mLAAmL;AAAA,IAClM;AAEA,QAAI,YAAY,cAAc,QAAQ,QAAQ,eAAe,YAAY,YAAY;AACnF,cAAQ,KAAK,gHAAgH;AAAA,IAC/H;AAEA,QAAI,YAAY,2BAA2B,QAAQ,QAAQ,4BAA4B,YAAY,yBAAyB;AAC1H,cAAQ,KAAK,6HAA6H;AAAA,IAC5I;AAEA,QAAI,YAAY,qBAAqB,QAAQ,QAAQ,sBAAsB,YAAY,mBAAmB;AACxG,cAAQ,KAAK,uHAAuH;AAAA,IACtI;AAAA,EACF,GAAG,CAAC,aAAa,OAAO,CAAC;AACzB,SAAoB,aAAAA,QAAM,cAAc,wBAAwB,UAAU;AAAA,IACxE,OAAO;AAAA,EACT,GAAG,QAAQ;AACb;AAEA,IAAI,gCAAgC,SAASmC,+BAA8B,MAAM;AAC/E,MAAI,KAAK,KAAK,IACV,YAAY,KAAK;AAErB,MAAI,wBAAwB,2BAA2B,GACnD,mBAAmB,sBAAsB;AAE7C,MAAI,YAAY,aAAAnC,QAAM,OAAO,KAAK;AAClC,MAAI,UAAU,aAAAA,QAAM,OAAO,IAAI;AAC/B,eAAAA,QAAM,gBAAgB,WAAY;AAChC,QAAI,CAAC,UAAU,WAAW,oBAAoB,QAAQ,YAAY,MAAM;AACtE,uBAAiB,MAAM,QAAQ,OAAO;AACtC,gBAAU,UAAU;AAAA,IACtB;AAGA,WAAO,WAAY;AACjB,UAAI,UAAU,WAAW,kBAAkB;AACzC,YAAI;AACF,2BAAiB,QAAQ;AACzB,oBAAU,UAAU;AAAA,QACtB,SAAS,GAAG;AAAA,QAMZ;AAAA,MACF;AAAA,IACF;AAAA,EACF,GAAG,CAAC,gBAAgB,CAAC;AACrB,SAAoB,aAAAA,QAAM,cAAc,OAAO;AAAA,IAC7C,KAAK;AAAA,IACL;AAAA,IACA;AAAA,EACF,CAAC;AACH;AAGA,IAAI,gCAAgC,SAASoC,+BAA8B,OAAO;AAChF,MAAI,KAAK,MAAM,IACX,YAAY,MAAM;AAEtB,6BAA2B;AAC3B,SAAoB,aAAApC,QAAM,cAAc,OAAO;AAAA,IAC7C;AAAA,IACA;AAAA,EACF,CAAC;AACH;AAEA,IAAI,mBAAmB,WAAW,gCAAgC;AAMlE,IAAI,YAAY,SAASqC,aAAY;AACnC,MAAI,wBAAwB,2CAA2C,mBAAmB,GACtF,SAAS,sBAAsB;AAEnC,SAAO;AACT;AASA,IAAI,uBAAuB,uBAAuB,iBAAiB,QAAQ;AAK3E,IAAI,cAAc,uBAAuB,QAAQ,QAAQ;AAKzD,IAAI,oBAAoB,uBAAuB,cAAc,QAAQ;AAKrE,IAAI,oBAAoB,uBAAuB,cAAc,QAAQ;AAKrE,IAAI,iBAAiB,uBAAuB,WAAW,QAAQ;AAK/D,IAAI,iBAAiB,uBAAuB,WAAW,QAAQ;AAK/D,IAAI,cAAc,uBAAuB,QAAQ,QAAQ;AAKzD,IAAI,mBAAmB,uBAAuB,aAAa,QAAQ;AAKnE,IAAI,iBAAiB,uBAAuB,WAAW,QAAQ;AAK/D,IAAI,iBAAiB,uBAAuB,WAAW,QAAQ;AAC/D,IAAI,iBAAiB,uBAAuB,WAAW,QAAQ;AAK/D,IAAI,yBAAyB,uBAAuB,mBAAmB,QAAQ;AAM/E,IAAI,0BAA0B,uBAAuB,oBAAoB,QAAQ;AAKjF,IAAI,8BAA8B,uBAAuB,wBAAwB,QAAQ;AAKzF,IAAI,4BAA4B,uBAAuB,sBAAsB,QAAQ;AAKrF,IAAI,iBAAiB,uBAAuB,WAAW,QAAQ;AAQ/D,IAAI,yBAAyB,uBAAuB,mBAAmB,QAAQ;AAK/E,IAAI,gCAAgC,uBAAuB,0BAA0B,QAAQ;AAK7F,IAAI,uBAAuB,uBAAuB,iBAAiB,QAAQ;AAK3E,IAAI,iCAAiC,uBAAuB,2BAA2B,QAAQ;", "names": ["obj", "useAttachEvent", "React", "decoratedCb", "usePrevious", "isUnknownObject", "isPromise", "isStripe", "isEqual", "pred", "extractAllowedOptionsUpdates", "validateStripe", "parseStripeProp", "registerWithStripeJs", "parseElementsContext", "Elements", "safeSetContext", "ctx", "PropTypes", "useElementsContextWithUseCase", "useElements", "ElementsConsumer", "parseCheckoutSdkContext", "extractCheckoutContextValue", "CheckoutProvider", "useCheckoutSdkContextWithUseCase", "useElementsOrCheckoutSdkContextWithUseCase", "useCheckout", "capitalized", "createElementComponent", "isServer", "ClientElement", "readyCallback", "ServerElement", "useEmbeddedCheckoutContext", "EmbeddedCheckoutProvider", "setStripeAndInitEmbeddedCheckout", "EmbeddedCheckoutClientElement", "EmbeddedCheckoutServerElement", "useStripe"]}