// src/contexts/ApiContext.tsx
import React, { createContext, useContext, useEffect, useState } from 'react';
import { VisionMaxAPI, ApiConfig } from '../api/visionMaxApi';

// Modify the interface to use the VisionMaxAPI type directly
interface ApiContextType {
  api: VisionMaxAPI;
  isInitialized: boolean;
}

// Create context with a default value
const ApiContext = createContext<ApiContextType | undefined>(undefined);

export const ApiProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [api, setApi] = useState<VisionMaxAPI | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    // Initialize the API with config
    const apiConfig: ApiConfig = {
      baseURL: import.meta.env.VITE_APP_API_BASE_URL || 'https://api.visionmaxfleet.com',
      token: localStorage.getItem('auth_token') || undefined
    };

    const apiInstance = new VisionMaxAPI(apiConfig);
    setApi(apiInstance);
    setIsInitialized(true);

    // Listen for token changes
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'auth_token' && e.newValue !== e.oldValue) {
        if (e.newValue) {
          apiInstance.setToken(e.newValue);
        } else {
          // Handle token removal - could redirect to login
          // Implementation depends on your auth flow
        }
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, []);

  if (!isInitialized || !api) {
    // You could return a loading indicator here
    return <div>Initializing API...</div>;
  }

  return (
    <ApiContext.Provider value={{ api, isInitialized }}>
      {children}
    </ApiContext.Provider>
  );
};

// Custom hook to use the API context
export const useApi = (): ApiContextType => {
  const context = useContext(ApiContext);
  if (context === undefined) {
    throw new Error('useApi must be used within an ApiProvider');
  }
  return context;
};