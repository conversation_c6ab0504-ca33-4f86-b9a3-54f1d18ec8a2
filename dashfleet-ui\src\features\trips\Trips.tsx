import React, { useState, useEffect } from 'react';
import { Search, Calendar, Filter, Download, RefreshCw, Play, Square, AlertCircle, Route, Navigation, ChevronLeft, ChevronRight } from 'lucide-react';
import { VisionMaxAPI } from '../../api/visionMaxApi'; // Adjust import path as needed

// Initialize API instance (you might want to get this from context or props)
const token = localStorage.getItem('authToken');

const api = new VisionMaxAPI({
  baseURL: import.meta.env.VITE_APP_API_BASE_URL?.replace('/V2', '') || 'https://api.visionmaxfleet.com',
  token
});


const TripList = () => {
  const [trips, setTrips] = useState([]);
  const [totalCount, setTotalCount] = useState(0);
  const [searchTerm, setSearchTerm] = useState('');
  const [dateRange, setDateRange] = useState({
    start: '04/22/2025',
    end: '05/21/2025'
  });
  const [filterType, setFilterType] = useState('All Trip');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [imageErrors, setImageErrors] = useState<Set<number>>(new Set());
  const [calendarView, setCalendarView] = useState({ month: 3, year: 2025 }); // April 2025
  const [selectingStart, setSelectingStart] = useState(true);
  const [showDatePicker, setShowDatePicker] = useState(false);

  const handleImageError = (tripId: number) => {
    setImageErrors(prev => new Set([...prev, tripId]));
  };

  // Convert date to timestamp
  const dateToTimestamp = (date) => {
    if (date instanceof Date) {
      return Math.floor(date.getTime() / 1000);
    }
    // Handle string dates
    if (typeof date === 'string') {
      return Math.floor(new Date(date).getTime() / 1000);
    }
    return Math.floor(Date.now() / 1000);
  };
  // Format date for display
  const formatDateForDisplay = (date) => {
    if (date instanceof Date) {
      return date.toLocaleDateString('en-US', {
        month: '2-digit',
        day: '2-digit',
        year: 'numeric'
      });
    }
    return new Date(date).toLocaleDateString('en-US', {
      month: '2-digit',
      day: '2-digit',
      year: 'numeric'
    });
  };

  // Quick date range functions
  const setQuickDateRange = (range) => {
    const now = new Date();
    let start, end;

    switch (range) {
      case 'last7':
        end = new Date(now);
        start = new Date(now.getTime() - 6 * 24 * 60 * 60 * 1000);
        break;
      case 'last14':
        end = new Date(now);
        start = new Date(now.getTime() - 13 * 24 * 60 * 60 * 1000);
        break;
      case 'last30':
        end = new Date(now);
        start = new Date(now.getTime() - 29 * 24 * 60 * 60 * 1000);
        break;
      case 'thisMonth':
        start = new Date(now.getFullYear(), now.getMonth(), 1);
        end = new Date(now.getFullYear(), now.getMonth() + 1, 0);
        break;
      case 'lastMonth':
        start = new Date(now.getFullYear(), now.getMonth() - 1, 1);
        end = new Date(now.getFullYear(), now.getMonth(), 0);
        break;
      case 'thisQuarter':
        const quarter = Math.floor(now.getMonth() / 3);
        start = new Date(now.getFullYear(), quarter * 3, 1);
        end = new Date(now.getFullYear(), quarter * 3 + 3, 0);
        break;
      case 'thisYear':
        start = new Date(now.getFullYear(), 0, 1);
        end = new Date(now.getFullYear(), 11, 31);
        break;
      default:
        return;
    }

    setDateRange({ start, end });
    setShowDatePicker(false);
  };

  // Calendar navigation
  const navigateCalendar = (direction) => {
    setCalendarView(prev => {
      const newMonth = direction === 'prev' ? prev.month - 1 : prev.month + 1;
      let newYear = prev.year;

      if (newMonth < 0) {
        return { month: 11, year: prev.year - 1 };
      } else if (newMonth > 11) {
        return { month: 0, year: prev.year + 1 };
      }

      return { month: newMonth, year: newYear };
    });
  };

  // Get calendar days
  const getCalendarDays = (month, year) => {
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const startDate = new Date(firstDay);
    startDate.setDate(startDate.getDate() - firstDay.getDay());

    const days = [];
    for (let i = 0; i < 42; i++) {
      const date = new Date(startDate);
      date.setDate(startDate.getDate() + i);
      days.push(date);
    }

    return days;
  };

  // Handle date selection
  const handleDateSelect = (date) => {
    if (selectingStart) {
      setDateRange(prev => ({ ...prev, start: date }));
      setSelectingStart(false);
    } else {
      if (date >= dateRange.start) {
        setDateRange(prev => ({ ...prev, end: date }));
      } else {
        setDateRange({ start: date, end: dateRange.start });
      }
      setSelectingStart(true);
    }
  };

  // Check if date is in range
  const isDateInRange = (date) => {
    return date >= dateRange.start && date <= dateRange.end;
  };

  // Check if date is selected
  const isDateSelected = (date) => {
    return date.toDateString() === dateRange.start.toString() ||
           date.toDateString() === dateRange.end.toString();
  };

  // Load trips from API
  const loadTrips = async (page = 1, resetData = false) => {
    try {
      setIsLoading(true);
      setError(null);

      const offset = (page - 1) * itemsPerPage;
      const since = dateToTimestamp(dateRange.start);
      const until = dateToTimestamp(dateRange.end) + 86399; // Add 23:59:59 to include the entire day

      const params = {
        offset,
        limit: itemsPerPage,
        page,
        pageSize: itemsPerPage,
        since,
        until,
        keyword: searchTerm.trim(),
        status: filterType === 'All Trip' ? undefined : [filterType]
      };

      const response = await api.getTrips(params);

      if (response.result) {
        setTrips(response.data);
        setTotalCount(response.count);

        if (resetData) {
          setCurrentPage(1);
        }
      } else {
        throw new Error(response.message || 'Failed to load trips');
      }
    } catch (err) {
      console.error('Error loading trips:', err);
      setError(err.message || 'Failed to load trips. Please try again.');
      setTrips([]);
      setTotalCount(0);
    } finally {
      setIsLoading(false);
    }
  };

  // Initial load and when dependencies change
  useEffect(() => {
    loadTrips(1, true);
  }, [dateRange, filterType]);

  // Load when page changes
  useEffect(() => {
    if (currentPage > 1) {
      loadTrips(currentPage);
    }
  }, [currentPage, itemsPerPage]);

  // Search with debounce
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (currentPage === 1) {
        loadTrips(1, true);
      } else {
        setCurrentPage(1);
      }
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [searchTerm]);

  // Format timestamp to readable date
  const formatDate = (timestamp) => {
    return new Date(timestamp * 1000).toLocaleString('en-US', {
      month: '2-digit',
      day: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });
  };

  // Format duration
  const formatDuration = (duration) => {
    if (duration === '---' || duration === 0) return '-';
    if (typeof duration === 'string') return duration;

    const hours = Math.floor(duration);
    const minutes = Math.floor((duration - hours) * 60);
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${Math.floor(((duration - hours) * 60 - minutes) * 60).toString().padStart(2, '0')}`;
  };

  // Format mileage
  const formatMileage = (mileage) => {
    if (mileage === 0) return '-';
    return (mileage / 1000).toFixed(2);
  };

  // Calculate pagination
  const totalPages = Math.ceil(totalCount / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = Math.min(startIndex + itemsPerPage, totalCount);

  const handleRefresh = () => {
    loadTrips(currentPage);
  };

  const handleClear = () => {
    setSearchTerm('');
    setFilterType('All Trip');
    setDateRange({
      start: '04/22/2025',
      end: '05/21/2025'
    });
  };

  const handleExport = async () => {
    try {
      setIsLoading(true);
      // Get all trips for export
      const params = {
        offset: 0,
        limit: totalCount,
        since: dateToTimestamp(dateRange.start),
        until: dateToTimestamp(dateRange.end) + 86399,
        keyword: searchTerm.trim(),
        status: filterType === 'All Trip' ? undefined : [filterType]
      };

      const response = await api.getTrips(params);

      if (response.result) {
        // Convert to CSV
        const csvContent = convertToCSV(response.data);
        downloadCSV(csvContent, 'trips-export.csv');
      }
    } catch (err) {
      console.error('Export failed:', err);
      setError('Export failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const convertToCSV = (data) => {
    const headers = ['Trip ID', 'Device Name', 'Status', 'Start Date', 'End Date', 'Driver', 'Mileage (KM)', 'Duration', 'Events'];
    const csvRows = [headers.join(',')];

    data.forEach(trip => {
      const row = [
        trip.id,
        `"${trip.device_name}"`,
        trip.status,
        `"${formatDate(trip.start_timestamp)}"`,
        trip.end_timestamp > 0 ? `"${formatDate(trip.end_timestamp)}"` : '"-"',
        `"${trip.driver_name || '-'}"`,
        formatMileage(trip.mileage),
        formatDuration(trip.duration),
        trip.events
      ];
      csvRows.push(row.join(','));
    });

    return csvRows.join('\n');
  };

  const downloadCSV = (csvContent, filename) => {
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    if (link.download !== undefined) {
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', filename);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  return (
    <div className="flex flex-col h-screen bg-gray-50">
    {/* Header */}
    <div className="bg-white border-b border-gray-200 px-6 py-4">
      <div className="flex items-center justify-between mb-4">
        <h1 className="text-2xl font-semibold text-gray-900">Trips</h1>
        <div className="flex items-center space-x-3">
          <button
            onClick={handleRefresh}
            className="flex items-center px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50"
            disabled={isLoading}
          >
            <RefreshCw className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </button>
          <button
            onClick={handleExport}
            className="flex items-center px-3 py-2 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700"
            disabled={isLoading}
          >
            <Download className="w-4 h-4 mr-2" />
            Export
          </button>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md flex items-center">
          <AlertCircle className="w-5 h-5 text-red-500 mr-2" />
          <span className="text-red-700">{error}</span>
          <button
            onClick={() => setError(null)}
            className="ml-auto text-red-500 hover:text-red-700"
          >
            ×
          </button>
        </div>
      )}

      {/* Filters */}
      <div className="flex items-center space-x-4">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <input
            type="text"
            placeholder="Search device name or driver name..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 pr-4 py-2 border border-gray-300 rounded-md w-80 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            disabled={isLoading}
          />
        </div>

      {/* Date Range Picker */}
      <div className="relative">
            <button
              onClick={() => setShowDatePicker(!showDatePicker)}
              className="flex items-center px-3 py-2 border border-gray-300 rounded-md bg-white hover:bg-gray-50 focus:ring-2 focus:ring-blue-500"
              disabled={isLoading}
            >
              <Calendar className="w-4 h-4 mr-2 text-gray-400" />
              <span className="text-sm">
                {formatDateForDisplay(dateRange.start)} → {formatDateForDisplay(dateRange.end)}
              </span>
            </button>

            {/* Date Picker Modal */}
            {showDatePicker && (
              <div className="absolute top-full left-0 mt-2 bg-white border border-gray-200 rounded-lg shadow-lg z-50 p-4 w-96">
                {/* Quick Selection Buttons */}
                <div className="mb-4">
                  <div className="grid grid-cols-3 gap-2 mb-2">
                    <button
                      onClick={() => setQuickDateRange('last7')}
                      className="px-3 py-1 text-xs border border-gray-300 rounded hover:bg-gray-50"
                    >
                      Last 7 Days
                    </button>
                    <button
                      onClick={() => setQuickDateRange('last14')}
                      className="px-3 py-1 text-xs border border-gray-300 rounded hover:bg-gray-50"
                    >
                      Last 14 Days
                    </button>
                    <button
                      onClick={() => setQuickDateRange('last30')}
                      className="px-3 py-1 text-xs border border-gray-300 rounded hover:bg-gray-50"
                    >
                      Last 30 Days
                    </button>
                  </div>
                  <div className="grid grid-cols-3 gap-2 mb-2">
                    <button
                      onClick={() => setQuickDateRange('thisMonth')}
                      className="px-3 py-1 text-xs border border-gray-300 rounded hover:bg-gray-50"
                    >
                      This Month
                    </button>
                    <button
                      onClick={() => setQuickDateRange('lastMonth')}
                      className="px-3 py-1 text-xs border border-gray-300 rounded hover:bg-gray-50"
                    >
                      Last Month
                    </button>
                    <button
                      onClick={() => setQuickDateRange('thisQuarter')}
                      className="px-3 py-1 text-xs border border-gray-300 rounded hover:bg-gray-50"
                    >
                      This Quarter
                    </button>
                  </div>
                  <button
                    onClick={() => setQuickDateRange('thisYear')}
                    className="px-3 py-1 text-xs border border-gray-300 rounded hover:bg-gray-50"
                  >
                    This Year
                  </button>
                </div>

                {/* Calendar Grid */}
                <div className="flex space-x-4">
                  {/* Left Calendar */}
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-2">
                      <button
                        onClick={() => navigateCalendar('prev')}
                        className="p-1 hover:bg-gray-100 rounded"
                      >
                        <ChevronLeft className="w-4 h-4" />
                      </button>
                      <h3 className="text-sm font-medium">
                        {new Date(calendarView.year, calendarView.month).toLocaleDateString('en-US', {
                          month: 'short',
                          year: 'numeric'
                        })}
                      </h3>
                      <button
                        onClick={() => navigateCalendar('next')}
                        className="p-1 hover:bg-gray-100 rounded"
                      >
                        <ChevronRight className="w-4 h-4" />
                      </button>
                    </div>

                    {/* Calendar Days */}
                    <div className="grid grid-cols-7 gap-1 text-center text-xs">
                      {['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa'].map(day => (
                        <div key={day} className="p-1 text-gray-500 font-medium">{day}</div>
                      ))}
                      {getCalendarDays(calendarView.month, calendarView.year).map((date, index) => {
                        const isCurrentMonth = date.getMonth() === calendarView.month;
                        const isSelected = isDateSelected(date);
                        const isInRange = isDateInRange(date);

                        return (
                          <button
                            key={index}
                            onClick={() => handleDateSelect(date)}
                            className={`p-1 text-xs rounded ${
                              !isCurrentMonth
                                ? 'text-gray-300'
                                : isSelected
                                  ? 'bg-blue-600 text-white'
                                  : isInRange
                                    ? 'bg-blue-100 text-blue-800'
                                    : 'hover:bg-gray-100'
                            }`}
                          >
                            {date.getDate()}
                          </button>
                        );
                      })}
                    </div>
                  </div>

                  {/* Right Calendar */}
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-2">
                      <button
                        onClick={() => navigateCalendar('prev')}
                        className="p-1 hover:bg-gray-100 rounded"
                      >
                        <ChevronLeft className="w-4 h-4" />
                      </button>
                      <h3 className="text-sm font-medium">
                        {new Date(calendarView.year, calendarView.month + 1).toLocaleDateString('en-US', {
                          month: 'short',
                          year: 'numeric'
                        })}
                      </h3>
                      <button
                        onClick={() => navigateCalendar('next')}
                        className="p-1 hover:bg-gray-100 rounded"
                      >
                        <ChevronRight className="w-4 h-4" />
                      </button>
                    </div>

                    <div className="grid grid-cols-7 gap-1 text-center text-xs">
                      {['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa'].map(day => (
                        <div key={day} className="p-1 text-gray-500 font-medium">{day}</div>
                      ))}
                      {getCalendarDays(calendarView.month + 1, calendarView.year).map((date, index) => {
                        // Handle month calculation for the right calendar
                        const rightCalendarMonth = calendarView.month + 1 > 11 ? 0 : calendarView.month + 1;
                        const rightCalendarYear = calendarView.month + 1 > 11 ? calendarView.year + 1 : calendarView.year;
                        const isCurrentMonth = date.getMonth() === rightCalendarMonth;
                        const isSelected = isDateSelected(date);
                        const isInRange = isDateInRange(date);

                        return (
                          <button
                            key={index}
                            onClick={() => handleDateSelect(date)}
                            className={`p-1 text-xs rounded ${
                              !isCurrentMonth
                                ? 'text-gray-300'
                                : isSelected
                                  ? 'bg-blue-600 text-white'
                                  : isInRange
                                    ? 'bg-blue-100 text-blue-800'
                                    : 'hover:bg-gray-100'
                            }`}
                          >
                            {date.getDate()}
                          </button>
                        );
                      })}
                    </div>
                  </div>
                </div>

                {/* OK Button */}
                <div className="flex justify-end mt-4">
                  <button
                    onClick={() => setShowDatePicker(false)}
                    className="px-4 py-2 bg-blue-600 text-white text-sm rounded hover:bg-blue-700"
                  >
                    OK
                  </button>
                </div>
              </div>
            )}
          </div>


        <select
          value={filterType}
          onChange={(e) => setFilterType(e.target.value)}
          className="px-5 py-2 border border-gray-300 rounded-md bg-white"
          disabled={isLoading}
        >
          <option value="All Trip">All Trip</option>
          <option value="Live">Live</option>
          <option value="Finish">Finished</option>
        </select>

        <button
          onClick={handleClear}
          className="px-5 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          disabled={isLoading}
        >
          Clear
        </button>
      </div>
    </div>

    {/* Results Count */}
    <div className="bg-white px-6 py-2 border-b border-gray-200">
      <span className="text-sm text-gray-600">
        Results: {isLoading ? 'Loading...' : totalCount}
      </span>
    </div>

    {/* Table */}
    <div className="flex-1 overflow-auto bg-white">
      {isLoading && trips.length === 0 ? (
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <RefreshCw className="w-8 h-8 text-gray-400 animate-spin mx-auto mb-2" />
            <p className="text-gray-500">Loading trips...</p>
          </div>
        </div>
      ) : trips.length === 0 ? (
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Navigation className="w-8 h-8 text-gray-400" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No trips found</h3>
            <p className="text-gray-500">Try adjusting your search criteria or date range.</p>
          </div>
        </div>
      ) : (
        <table className="w-full">
          <thead className="bg-gray-50 sticky top-0">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Trip</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Start Date & Time</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">End Date & Time</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Asset ID</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Driver</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mileage (KM)</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Duration</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {trips.map((trip, index) => (
              <tr key={trip.id} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    {trip.status === 'Live' ? (
                      <div className="flex items-center text-green-600">
                        <Play className="w-4 h-4 mr-2 fill-current" />
                        <span className="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">
                          Live
                        </span>
                      </div>
                    ) : (
                      <div className="flex items-center text-gray-600">
                        <Square className="w-4 h-4 mr-2 fill-current" />
                        <span className="px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 rounded-full">
                          Finished
                        </span>
                      </div>
                    )}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {formatDate(trip.start_timestamp)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {trip.end_timestamp > 0 ? formatDate(trip.end_timestamp) : '-'}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {trip.device_name}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center mr-3">
                      {trip.clock_in_status?.headshot_image_url && !imageErrors.has(trip.id) ? (
                        <img
                          src={trip.clock_in_status.headshot_image_url}
                          alt="Driver"
                          className="w-8 h-8 rounded-full object-cover"
                          onError={() => handleImageError(trip.id)}
                        />
                      ) : (
                        <span className="text-xs text-gray-600">👤</span>
                      )}
                    </div>
                    <span className="text-sm text-gray-900">
                      {trip.driver_name || '-'}
                    </span>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {formatMileage(trip.mileage)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {formatDuration(trip.duration)}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      )}
    </div>

    {/* Pagination */}
    <div className="bg-white border-t border-gray-200 px-6 py-3">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-700">Items per page:</span>
          <select
            value={itemsPerPage}
            onChange={(e) => {
              setItemsPerPage(Number(e.target.value));
              setCurrentPage(1);
            }}
            className="px-2 py-1 border border-gray-300 rounded text-sm"
            disabled={isLoading}
          >
            <option value={10}>10</option>
            <option value={25}>25</option>
            <option value={50}>50</option>
            <option value={100}>100</option>
          </select>
        </div>

        <div className="flex items-center space-x-2">
          <button
            onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
            disabled={currentPage === 1 || isLoading}
            className="px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
          >
            Previous
          </button>

          <div className="flex space-x-1">
            {(() => {
              const pages = [];
              const maxVisiblePages = 5;
              let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
              let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

              // Adjust startPage if we're near the end
              if (endPage - startPage + 1 < maxVisiblePages) {
                startPage = Math.max(1, endPage - maxVisiblePages + 1);
              }

              for (let i = startPage; i <= endPage; i++) {
                pages.push(
                  <button
                    key={i}
                    onClick={() => setCurrentPage(i)}
                    disabled={isLoading}
                    className={`px-3 py-1 text-sm rounded ${
                      currentPage === i
                        ? 'bg-blue-600 text-white'
                        : 'border border-gray-300 hover:bg-gray-50 disabled:opacity-50'
                    }`}
                  >
                    {i}
                  </button>
                );
              }

              if (endPage < totalPages) {
                pages.push(
                  <span key="ellipsis" className="px-2 py-1 text-sm text-gray-500">...</span>
                );
                pages.push(
                  <button
                    key={totalPages}
                    onClick={() => setCurrentPage(totalPages)}
                    disabled={isLoading}
                    className={`px-3 py-1 text-sm rounded ${
                      currentPage === totalPages
                        ? 'bg-blue-600 text-white'
                        : 'border border-gray-300 hover:bg-gray-50 disabled:opacity-50'
                    }`}
                  >
                    {totalPages}
                  </button>
                );
              }

              return pages;
            })()}
          </div>

          <button
            onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
            disabled={currentPage === totalPages || isLoading}
            className="px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
          >
            Next
          </button>
        </div>

        <span className="text-sm text-gray-700">
          {totalCount > 0 ? `${startIndex + 1}-${Math.min(endIndex, totalCount)} of ${totalCount}` : '0 of 0'}
        </span>
      </div>
    </div>
  </div>
  );
};

export default TripList;