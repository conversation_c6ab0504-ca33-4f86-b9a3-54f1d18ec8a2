// Test utility to verify API connectivity
import { env } from './env';

export const testApiConnection = async () => {
  try {
    console.log('Testing API connection...');
    console.log('API Base URL:', env.apiBaseUrl);
    console.log('Environment:', import.meta.env.PROD ? 'Production' : 'Development');
    
    // Test a simple endpoint
    const response = await fetch(`${env.apiBaseUrl}/dashboard`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': 'WnbpXF1lJ1vKePo1C5pCXbgVGbkwehE8wMvX5LV9ikQ',
      },
    });
    
    console.log('Response status:', response.status);
    console.log('Response headers:', Object.fromEntries(response.headers.entries()));
    
    if (response.ok) {
      console.log('✅ API connection successful');
      return true;
    } else {
      console.log('❌ API connection failed:', response.statusText);
      return false;
    }
  } catch (error) {
    console.error('❌ API connection error:', error);
    return false;
  }
};

// Call this in browser console to test: testApiConnection()
if (typeof window !== 'undefined') {
  (window as any).testApiConnection = testApiConnection;
}
