// src/App.tsx
import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';

// Layouts
import MainLayout from '../layouts/MainLayout';
import AuthLayout from '../layouts/AuthLayout';

// Authentication
import Login from '../features/auth/Login'
import ForgotPassword from '../features/auth/ForgotPassword';
import ResetPassword from '../features/auth/ResetPassword';
import { ApiProvider } from '../contexts/ApiContext';

// Protected routes
import Dashboard from '../features/dashboard/Dashboard';
import Devices from '../features/devices/Devices';
// import DeviceDetails from '../features/devices/DeviceDetails'
import Events from '../features/events/Events';
import EventDetails from '../features/events/Events'
import Trips from '../features/trips/Trips'
import TripDetails from '../features/trips/TripDetails';
import Drivers from '../features/drivers/Drivers';
import DriverDetails from '../features/drivers/DriverDetails';
import Vehicles from '../features/vehicles/Vehicles';
import VehicleDetails from '../features/vehicles/VehicleDetails';
import Settings from '../features/settings/Settings';

// Authentication guard
import ProtectedRoute from '../features/auth/ProtectedRoute';

import { VisionMaxAPI } from '@/api/visionMaxApi';
import { env } from '@/utils/env';

import ConfigurationPage from '@/features/configuration/ConfigurationPage';
import TripReport from '@/features/trips/TripReport';
import SubscriptionPage from '@/features/payment/SubscriptionPage';

// Initialize API - ensure this is done outside component to avoid recreating on each render
const api = new VisionMaxAPI({
  baseURL: env.apiBaseUrl.replace('/V2', ''), // Remove /V2 suffix for VisionMaxAPI
  token: localStorage.getItem('authToken') || undefined
});

const App: React.FC = () => {
  return (
    <Router>

      <Routes>
        {/* Auth routes */}
        <Route element={<AuthLayout />}>
          <Route path="/login" element={<Login />} />
          <Route path="/forgot-password" element={<ForgotPassword />} />
          <Route path="/reset-password" element={<ResetPassword />} />
        </Route>

        {/* Protected routes */}
        <Route element={<ProtectedRoute />}>
          <Route element={<MainLayout />}>
            {/* Dashboard */}
            <Route path="/dashboard" element={<Dashboard />} />

            {/* Devices */}
            <Route path="/devices" element={<Devices />} />
            {/* <Route path="/devices/:id" element={<DeviceDetails />} /> */}

            {/* Events */}
            <Route path="/events" element={<Events />} />
            <Route path="/events/:id" element={<EventDetails />} />

            {/* Trips */}
            <Route path="/trips" element={<Trips />} />
            <Route path="/report" element={<TripReport />} />
            <Route path="/trips/:id" element={<TripDetails />} />

            {/* Drivers */}
            <Route path="/drivers" element={<Drivers />} />
            <Route path="/drivers/:id" element={<DriverDetails />} />

            {/* Vehicles */}
            <Route path="/vehicles" element={<Vehicles />} />
            <Route path="/vehicles/:id" element={<VehicleDetails />} />

            {/* Configuration */}
            <Route path="configuration" element={<ConfigurationPage />} />
            {/* Settings */}
            <Route path="/settings" element={<Settings />} />
            <Route path="/payment" element={<SubscriptionPage />} />
          </Route>
        </Route>

        {/* Redirect to dashboard by default */}
        <Route path="/" element={<Navigate to="/dashboard" replace />} />

        {/* 404 - Not found */}
        <Route path="*" element={
          <div className="flex items-center justify-center h-screen">
            <div className="text-center">
              <h1 className="text-6xl font-bold text-gray-800">404</h1>
              <p className="text-xl mt-4">Page not found</p>
              <button
                className="mt-8 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
                onClick={() => window.location.href = '/dashboard'}
              >
                Back to Dashboard
              </button>
            </div>
          </div>
        } />
      </Routes>
    </Router>
  );
};

export default App;
