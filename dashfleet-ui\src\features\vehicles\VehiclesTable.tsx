// src/features/vehicles/VehiclesTable.tsx
import React, { useState } from 'react';
import { format } from 'date-fns';
import { Check, X, AlertTriangle, MapPin, User, Calendar, Trash2, Power } from 'lucide-react';
import { VehicleType } from '@/api/visionMaxApi';

// Import the EnhancedVehicle interface, or define it here if needed
interface Vehicle {
  id?: number;
  license_plate: string;
  vin: string;
  model: string;
  status: number;
  make?: string;
  type?: VehicleType;
  year?: number;
  lastActiveDate?: Date;
  updated_at?: number;
  driverName?: string;
  location?: string;
  mileage?: number;
  vehicle_id: number; // Add this property that's referenced in the component
}

interface VehiclesTableProps {
  vehicles: Vehicle[];
  isLoading?: boolean;
  onRowClick: (vehicleId: number) => void;
  onVehicleActivate?: (vehicleId: number) => Promise<void>;
  onVehicleDeactivate?: (vehicleId: number) => Promise<void>;
  onVehicleDelete?: (vehicleId: number) => Promise<void>;
}

export const VehiclesTable: React.FC<VehiclesTableProps> = ({
  vehicles,
  isLoading = false,
  onRowClick,
  onVehicleDeactivate,
  onVehicleDelete,
}) => {
  const [actionLoading, setActionLoading] = useState<{[key: number]: 'deactivate' | 'delete' | null}>({});

  // Format date
  const formatDate = (timestamp?: number) => {
    if (!timestamp) return 'N/A';
    try {
      return format(new Date(timestamp * 1000), 'MMM d, yyyy');
    } catch (error) {
      return 'Invalid date';
    }
  };

  // Get status indicator
  const getStatusIndicator = (status: number) => {
    switch (status) {
      case 1:
        return (
          <div className="flex items-center">
            <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
            <span className="text-green-600">Active</span>
          </div>
        );
      case 0:
        return (
          <div className="flex items-center">
            <span className="w-2 h-2 bg-gray-500 rounded-full mr-2"></span>
            <span className="text-gray-600">Inactive</span>
          </div>
        );
      case 2:
        return (
          <div className="flex items-center">
            <span className="w-2 h-2 bg-yellow-500 rounded-full mr-2"></span>
            <span className="text-yellow-600">Maintenance</span>
          </div>
        );
      default:
        return (
          <div className="flex items-center">
            <span className="w-2 h-2 bg-gray-500 rounded-full mr-2"></span>
            <span className="text-gray-600">{status}</span>
          </div>
        );
    }
  };

  // Handle deactivate action
  const handleDeactivate = async (e: React.MouseEvent, vehicleId: number) => {
    e.stopPropagation(); // Prevent row click
    
    if (!onVehicleDeactivate) return;
    
    setActionLoading(prev => ({ ...prev, [vehicleId]: 'deactivate' }));
    
    try {
      alert(vehicleId);
      await onVehicleDeactivate(vehicleId);
    } catch (error) {
      console.error('Failed to deactivate vehicle:', error);
      // You might want to show a toast notification here
    } finally {
      setActionLoading(prev => ({ ...prev, [vehicleId]: null }));
    }
  };

  // Handle delete action
  const handleDelete = async (e: React.MouseEvent, vehicleId: number) => {
    e.stopPropagation(); // Prevent row click
    
    if (!onVehicleDelete) return;
    
    // Show confirmation dialog
    const confirmed = window.confirm('Are you sure you want to delete this vehicle? This action cannot be undone.');
    if (!confirmed) return;
    
    setActionLoading(prev => ({ ...prev, [vehicleId]: 'delete' }));
    
    try {
      await onVehicleDelete(vehicleId);
    } catch (error) {
      console.error('Failed to delete vehicle:', error);
      // You might want to show a toast notification here
    } finally {
      setActionLoading(prev => ({ ...prev, [vehicleId]: null }));
    }
  };

  return (
    <div className="overflow-x-auto">
      <table className="min-w-full border-collapse">
        <thead>
          <tr className="bg-gray-50 border-b border-gray-200">
            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Vehicle
            </th>
            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              License Plate
            </th>
            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Status
            </th>
            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Last Active
            </th>
            <th className="px-actionLoading4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              VIN
            </th>
            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Actions
            </th>
          </tr>
        </thead>
        <tbody className={`divide-y divide-gray-200 ${isLoading ? 'opacity-50' : ''}`}>
          {vehicles.map((vehicle) => (
            <tr 
              key={vehicle.id}
              onClick={() => onRowClick(vehicle.id)}
              className="hover:bg-gray-50 cursor-pointer transition-colors"
            >
              <td className="px-4 py-4">
                <div className="flex items-center">
                  <div className="ml-4">
                    <div className="text-sm font-medium text-gray-900">
                      {vehicle.make} {vehicle.model}
                    </div>
                    <div className="text-sm text-gray-500">
                      ID: {vehicle.vehicle_id || vehicle.id}
                    </div>
                  </div>
                </div>
              </td>
              <td className="px-4 py-4">
                <div className="text-sm text-gray-900">{vehicle.license_plate}</div>
              </td>
              <td className="px-4 py-4">
                {getStatusIndicator(vehicle.status)}
              </td>
              <td className="px-4 py-4">
                <div className="text-sm text-gray-900">{formatDate(vehicle.updated_at)}</div>
              </td>
              <td className="px-4 py-4">
                <div className="text-sm text-gray-900">{vehicle.vin || 'N/A'}</div>
              </td>
              <td className="px-4 py-4">
                <div className="flex items-center space-x-2">
                  {/* Deactivate Button */}
                  { vehicle.status === 1 && (
                    
                    <button
                      onClick={(e) => handleDeactivate(e, vehicle.id)}
                      disabled={actionLoading[vehicle.id] === 'deactivate'}
                      className="inline-flex items-center px-2 py-1 text-xs font-medium text-yellow-700 bg-yellow-100 border border-yellow-300 rounded-md hover:bg-yellow-200 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                      title="Deactivate Vehicle"
                    >   <Power className="w-3 h-3 mr-1" />
                    Deactivate
                    </button>
                  )} 
                  
                  
                  {/* Delete Button */}
                  {onVehicleDelete && (
                    <button
                      onClick={(e) => handleDelete(e, vehicle.id)}
                      disabled={actionLoading[vehicle.id] === 'delete'}
                      className="inline-flex items-center px-2 py-1 text-xs font-medium text-red-700 bg-red-100 border border-red-300 rounded-md hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                      title="Delete Vehicle"
                    >
                      {actionLoading[vehicle.id] === 'delete' ? (
                        <div className="w-3 h-3 border border-red-600 border-t-transparent rounded-full animate-spin mr-1"></div>
                      ) : (
                        <Trash2 className="w-3 h-3 mr-1" />
                      )}
                      Delete
                    </button>
                  )}
                </div>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
      
      {vehicles.length === 0 && !isLoading && (
        <div className="text-center py-8 text-gray-500">
          No vehicles found matching your criteria.
        </div>
      )}
    </div>
  );
};

export default VehiclesTable;