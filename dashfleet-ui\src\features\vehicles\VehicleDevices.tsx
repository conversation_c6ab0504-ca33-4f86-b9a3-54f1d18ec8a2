// src/features/vehicles/VehicleDevices.tsx
import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { format } from 'date-fns';
import { Cpu, Plus, Check, AlertTriangle, WifiOff, Camera, Wifi } from 'lucide-react';

import { Card, CardContent, CardHeader, CardTitle } from '../../components/common/Card';
import { Button } from '../../components/common/Button';
import { Spinner } from '../../components/common/Spinner';
import { Alert } from '../../components/common/Alert';

// Import the VisionMaxAPI client and API context
import { DeviceInfo, NewDeviceInfo, VehiclesDetail } from '../../api/visionMaxApi';
import { useApi } from '../../contexts/ApiContext';

interface VehicleDevicesProps {
  vehicleId: string;
}

// Define the device interface we need for the UI
interface DeviceUIData {
  id: string;
  type: string;
  model: string;
  status: string;
  serialNumber: string;
  installedDate?: string;
  lastActive?: string;
  firmware?: string;
  health: string;
}

export const VehicleDevices: React.FC<VehicleDevicesProps> = ({ vehicleId }) => {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [vehicle, setVehicle] = useState<VehiclesDetail | null>(null);
  const [devices, setDevices] = useState<DeviceUIData[]>([]);

  // Get API client instance from context
  const { api } = useApi();

  // Fetch vehicle and device data
  const fetchData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Get vehicle details
      const vehicleData = await api.getVehicleById(parseInt(vehicleId));
      setVehicle(vehicleData);

      // If vehicle has paired device, get device details
      if (vehicleData.paired_device) {
        const deviceId = vehicleData.paired_device.id;
        // Get device details directly or as part of a list
        const deviceInfo = await api.getDevices({ cid: vehicleData.paired_device.serial_number });

        // Map API data to UI format
        if (deviceInfo.data.length > 0) {
          const mappedDevices = deviceInfo.data.map((device: NewDeviceInfo) => ({
            id: device.cid, // Use cid as the primary identifier (string)
            type: device.sku_name?.toLowerCase().includes('cam') ? 'camera' : 'device',
            model: device.product_name || device.device_name,
            status: device.is_online ? 'Online' : 'Offline',
            serialNumber: device.serial_number,
            installedDate: device.active_since ?
              new Date(device.active_since * 1000).toISOString() :
              device.active_at,
            lastActive: device.last_connected_timestamp ?
              new Date(device.last_connected_timestamp * 1000).toISOString() :
              device.keep_alive_timestamp ?
                new Date(device.keep_alive_timestamp * 1000).toISOString() :
                undefined,
            firmware: device.firmware_version,
            health: device.cover_status === 'normal' ? 'Healthy' :
              (device.cover_status === 'error' ? 'Error' : 'Warning')
          }));
          setDevices(mappedDevices);
        }
      }
    } catch (err) {
      console.error('Error fetching vehicle devices:', err);
      setError('Failed to load vehicle devices');
    } finally {
      setIsLoading(false);
    }
  };

  // Initial data fetch
  useEffect(() => {
    fetchData();
  }, [vehicleId]);

  // Format date
  const formatDate = (dateString?: string) => {
    if (!dateString) return 'N/A';
    try {
      return format(new Date(dateString), 'MMM d, yyyy');
    } catch (error) {
      return dateString;
    }
  };

  // Get status indicator
  const getStatusIndicator = (status: string) => {
    switch (status) {
      case 'Online':
        return (
          <div className="flex items-center">
            <Wifi size={16} className="text-green-500 mr-2" />
            <span className="text-green-600">Online</span>
          </div>
        );
      case 'Offline':
        return (
          <div className="flex items-center">
            <WifiOff size={16} className="text-gray-500 mr-2" />
            <span className="text-gray-600">Offline</span>
          </div>
        );
      default:
        return (
          <div className="flex items-center">
            <span className="w-2 h-2 bg-gray-500 rounded-full mr-2"></span>
            <span className="text-gray-600">{status}</span>
          </div>
        );
    }
  };

  // Get health indicator
  const getHealthIndicator = (health: string) => {
    switch (health) {
      case 'Healthy':
        return (
          <div className="flex items-center">
            <Check size={16} className="text-green-500 mr-2" />
            <span className="text-green-600">Healthy</span>
          </div>
        );
      case 'Warning':
        return (
          <div className="flex items-center">
            <AlertTriangle size={16} className="text-yellow-500 mr-2" />
            <span className="text-yellow-600">Warning</span>
          </div>
        );
      case 'Error':
        return (
          <div className="flex items-center">
            <AlertTriangle size={16} className="text-red-500 mr-2" />
            <span className="text-red-600">Error</span>
          </div>
        );
      default:
        return (
          <div className="flex items-center">
            <span className="w-2 h-2 bg-gray-500 rounded-full mr-2"></span>
            <span className="text-gray-600">{health}</span>
          </div>
        );
    }
  };

  // Handle add device
  const handleAddDevice = async () => {
    // This would involve:
    // 1. Getting available devices from api.getAvailableDevices()
    // 2. Selecting a device to associate with this vehicle
    // 3. Making the association with api.setVehicleOfDevice()
    alert('Add device functionality would be implemented here');
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="flex justify-center items-center p-8">
        <Spinner size="large" color="primary" />
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <Alert
        type="error"
        title="Error Loading Devices"
        message="There was a problem loading the vehicle devices. Please try again."
        action={{
          label: 'Retry',
          onClick: fetchData
        }}
      />
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-lg font-medium">Vehicle Devices</h2>
        <Button leftIcon={<Plus size={16} />} onClick={handleAddDevice}>
          Add Device
        </Button>
      </div>

      {/* Devices list */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {devices.length > 0 ? (
          devices.map((device, index) => (
            <Card key={index} className="overflow-hidden">
              <CardHeader className="pb-2 flex flex-row items-center justify-between">
                <CardTitle className="flex items-center">
                  {device.type === 'camera' ? (
                    <Camera size={18} className="mr-2" />
                  ) : (
                    <Cpu size={18} className="mr-2" />
                  )}
                  {device.model}
                </CardTitle>
                <div className="text-sm">
                  {getStatusIndicator(device.status)}
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="grid grid-cols-2 gap-2">
                    <div>
                      <div className="text-xs text-gray-500">Device ID</div>
                      <div className="text-sm font-medium">{device.id}</div>
                    </div>
                    <div>
                      <div className="text-xs text-gray-500">Serial Number</div>
                      <div className="text-sm font-medium">{device.serialNumber}</div>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-2">
                    <div>
                      <div className="text-xs text-gray-500">Installed</div>
                      <div className="text-sm font-medium">{formatDate(device.installedDate)}</div>
                    </div>
                    <div>
                      <div className="text-xs text-gray-500">Last Active</div>
                      <div className="text-sm font-medium">{device.lastActive ? formatDate(device.lastActive) : 'Never'}</div>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-2">
                    <div>
                      <div className="text-xs text-gray-500">Firmware</div>
                      <div className="text-sm font-medium">{device.firmware || 'N/A'}</div>
                    </div>
                    <div>
                      <div className="text-xs text-gray-500">Health</div>
                      <div className="text-sm font-medium">
                        {getHealthIndicator(device.health)}
                      </div>
                    </div>
                  </div>

                  <div className="pt-2 flex justify-end">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => navigate(`/devices/${device.id}`)}
                    >
                      View Details
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        ) : (
          <Card className="md:col-span-2">
            <CardContent className="p-8">
              <div className="text-center">
                <Cpu size={48} className="mx-auto text-gray-300 mb-4" />
                <h3 className="text-lg font-medium text-gray-900">No devices installed</h3>
                <p className="mt-1 text-sm text-gray-500">
                  This vehicle doesn't have any devices installed yet.
                </p>
                <Button className="mt-4" onClick={handleAddDevice}>
                  Add First Device
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};

export default VehicleDevices;