# Vercel Deployment Troubleshooting Guide

## Common Vercel Errors & Solutions

### 1. **Build Command Not Found / "vite: command not found"**

**Solution**: Updated `vercel.json` to specify Vite framework:
```json
{
  "framework": "vite",
  "buildCommand": "npm run build",
  "outputDirectory": "dist"
}
```

### 2. **Node.js Version Issues**

**Solution**: Added `.nvmrc` file specifying Node 18:
```
18
```

### 3. **Environment Variables Not Working**

**In Vercel Dashboard**:
1. Go to Project Settings → Environment Variables
2. Add these variables (optional, fallbacks exist):
   - `VITE_APP_API_BASE_URL` = `https://api.visionmaxfleet.com/V2`
   - `VITE_APP_GOOGLE_MAPS_API_KEY` = `your-google-maps-key`
   - `VITE_APP_ENABLE_SOCKET_IO` = `false`
   - `VITE_APP_DEBUG_MODE` = `false`

### 4. **API/CORS Errors**

**Check**:
- Proxy function exists at `/api/proxy.js` ✅
- Vercel rewrites configured ✅
- CORS headers set ✅

### 5. **Build Timeout**

**Solution**: Optimize build in `vite.config.js`:
```javascript
export default defineConfig({
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          router: ['react-router-dom'],
          // ... other chunks
        }
      }
    }
  }
});
```

### 6. **Static File Issues**

**Check**:
- Files in `/public` directory are accessible
- `index.html` exists in build output
- SPA routing configured with rewrites

### 7. **Function Size Limits**

**If proxy function is too large**:
- Vercel has 50MB limit for serverless functions
- Our proxy is small (~3KB) ✅

## Deployment Steps

1. **Push to Git**:
   ```bash
   git add .
   git commit -m "Fix Vercel deployment configuration"
   git push
   ```

2. **In Vercel Dashboard**:
   - Import project from Git
   - Set root directory to `dashfleet-ui`
   - Framework should auto-detect as "Vite"
   - Build command: `npm run build`
   - Output directory: `dist`

3. **Deploy**:
   - Click "Deploy"
   - Monitor build logs for errors

## Debug Commands

**Local testing**:
```bash
# Test build locally
npm run build

# Test preview
npm run preview

# Check TypeScript
npm run type-check
```

**Check Vercel logs**:
1. Go to Vercel Dashboard
2. Click on your deployment
3. View "Functions" tab for proxy logs
4. View "Build Logs" for build errors

## Current Status ✅

- [x] Build working locally
- [x] TypeScript compilation passing
- [x] Vite configuration optimized
- [x] CORS proxy configured
- [x] Environment variables set up
- [x] Node.js version specified
- [x] Framework detection configured

## If Still Having Issues

**Please share**:
1. Exact error message from Vercel
2. Build logs from Vercel dashboard
3. Which step is failing (build, deploy, runtime)

**Common error patterns**:
- `Command "vite" not found` → Framework detection issue
- `Module not found` → Import path issues
- `CORS error` → Proxy configuration issue
- `404 on refresh` → SPA routing issue
- `Build timeout` → Bundle size issue
