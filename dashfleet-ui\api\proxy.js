// Vercel serverless function to proxy API requests
export default async function handler(req, res) {
  // Set CORS headers for all requests
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, x-api-key');

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  const { method, body, query } = req;

  // Extract the API path from the query
  const apiPath = query.path ? (Array.isArray(query.path) ? query.path.join('/') : query.path) : '';

  // Build the target URL
  const targetUrl = `https://api.visionmaxfleet.com/V2/${apiPath}`;

  // Prepare headers
  const headers = {
    'Content-Type': 'application/json',
    'x-api-key': 'WnbpXF1lJ1vKePo1C5pCXbgVGbkwehE8wMvX5LV9ikQ',
  };

  // Add authorization header if present
  if (req.headers.authorization) {
    headers.Authorization = req.headers.authorization;
  }

  // Build query string (excluding 'path' parameter)
  const queryParams = Object.fromEntries(
    Object.entries(query).filter(([key]) => key !== 'path')
  );
  const queryString = Object.keys(queryParams).length > 0
    ? '?' + new URLSearchParams(queryParams).toString()
    : '';

  const fullUrl = targetUrl + queryString;

  try {
    console.log(`Proxying ${method} request to: ${fullUrl}`);

    // Make the request to the actual API
    const fetchOptions = {
      method,
      headers,
    };

    // Add body for non-GET requests
    if (method !== 'GET' && method !== 'HEAD' && body) {
      fetchOptions.body = JSON.stringify(body);
    }

    const response = await fetch(fullUrl, fetchOptions);
    const data = await response.text();

    // Set response status
    res.status(response.status);

    // Set content type
    const contentType = response.headers.get('content-type');
    if (contentType) {
      res.setHeader('Content-Type', contentType);
    }

    // Try to parse and return JSON, otherwise return as text
    try {
      const jsonData = JSON.parse(data);
      res.json(jsonData);
    } catch {
      res.send(data);
    }
  } catch (error) {
    console.error('Proxy error:', error);
    res.status(500).json({
      error: 'Proxy request failed',
      details: error.message,
      url: fullUrl
    });
  }
}
