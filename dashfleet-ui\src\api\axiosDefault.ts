import axios from 'axios';
import { env } from '@/utils/env';

// Set base URL for API requests
axios.defaults.baseURL = env.apiBaseUrl;

// Set default headers
axios.defaults.headers.common['Content-Type'] = 'application/json';
axios.defaults.headers.common['x-api-key'] = 'WnbpXF1lJ1vKePo1C5pCXbgVGbkwehE8wMvX5LV9ikQ';
// axios.defaults.headers.common['origin'] = 'http://localhost:3000';

// Optional: Add an interceptor for request configuration
axios.interceptors.request.use(
  (config) => {
    // You can add authentication token here if needed
    const token = localStorage.getItem('authToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Optional: Add an interceptor for response handling
axios.interceptors.response.use(
  (response) => response,
  (error) => {
    // Handle common error scenarios
    if (error.response) {
      switch (error.response.status) {
        case 401:
          // Unauthorized - maybe redirect to login or refresh token
          console.error('Unauthorized access');
          break;
        case 403:
          // Forbidden
          console.error('Access forbidden');
          break;
        case 404:
          // Not found
          console.error('Resource not found');
          break;
        case 500:
          // Server error
          console.error('Internal server error');
          break;
        default:
          console.error('An error occurred');
      }
    }
    return Promise.reject(error);
  }
);

export default axios;