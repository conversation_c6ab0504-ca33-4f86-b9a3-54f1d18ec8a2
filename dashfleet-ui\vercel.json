{"version": 2, "buildCommand": "npm run build", "outputDirectory": "dist", "installCommand": "npm install", "framework": null, "rewrites": [{"source": "/api/v2/(.*)", "destination": "/api/proxy?path=$1"}, {"source": "/(.*)", "destination": "/index.html"}], "headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization, x-api-key"}]}], "env": {"VITE_APP_API_BASE_URL": "@vite_app_api_base_url", "VITE_APP_GOOGLE_MAPS_API_KEY": "@vite_app_google_maps_api_key", "VITE_APP_ENABLE_SOCKET_IO": "@vite_app_enable_socket_io", "VITE_APP_DEBUG_MODE": "@vite_app_debug_mode"}}