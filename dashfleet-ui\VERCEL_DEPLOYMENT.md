# Vercel Deployment Guide for DashFleet UI

## Prerequisites
- Vercel account (sign up at https://vercel.com)
- Git repository (GitHub, GitLab, or Bitbucket)

## Deployment Steps

### 1. Push to Git Repository
Make sure your code is pushed to a Git repository that <PERSON>ercel can access.

### 2. Connect to Vercel
1. Go to https://vercel.com/dashboard
2. Click "New Project"
3. Import your Git repository
4. Select the `dashfleet-ui` folder as the root directory

### 3. Configure Build Settings
Vercel should automatically detect the settings from `vercel.json`, but verify:
- **Framework Preset**: Other
- **Build Command**: `npm run build`
- **Output Directory**: `dist`
- **Install Command**: `npm install`

### 4. Environment Variables (Optional)
If you want to override the hardcoded values, add these environment variables in Vercel dashboard:

- `VITE_APP_API_BASE_URL` (default: https://api.visionmaxfleet.com/V2)
- `VITE_APP_GOOGLE_MAPS_API_KEY` (for Google Maps functionality)
- `VITE_APP_ENABLE_SOCKET_IO` (default: false)
- `VITE_APP_DEBUG_MODE` (default: false)

### 5. Deploy
Click "Deploy" and wait for the build to complete.

## Current Configuration

### CORS Solution
To fix CORS errors on Vercel, the project now uses:
- **API Proxy**: Serverless function at `/api/proxy.js` that proxies requests to the actual API
- **Environment-aware routing**: Uses proxy in production, direct API in development
- **Automatic switching**: No manual configuration needed

### Hardcoded Values (No Environment Variables Needed)
The following values are hardcoded and will work without environment variables:
- API Base URL: `https://api.visionmaxfleet.com/V2` (proxied in production)
- API Key: `WnbpXF1lJ1vKePo1C5pCXbgVGbkwehE8wMvX5LV9ikQ`
- VisionMax API URL: `https://api.visionmaxfleet.com`

### Features
- ✅ Single Page Application (SPA) routing configured
- ✅ CORS bypass using serverless proxy
- ✅ Optimized build with code splitting
- ✅ Source maps enabled for debugging
- ✅ Environment variable support (optional)
- ✅ Automatic development/production API switching

## Troubleshooting

### Build Issues
If build fails, check:
1. Node.js version (requires >=18.0.0)
2. TypeScript compilation errors
3. Missing dependencies

### Runtime Issues
- Check browser console for errors
- Verify API endpoints are accessible
- Check network requests in browser dev tools

## Post-Deployment
1. Test all major features
2. Verify API connectivity
3. Check routing works correctly
4. Test on different devices/browsers

## Custom Domain (Optional)
To add a custom domain:
1. Go to your project settings in Vercel
2. Navigate to "Domains"
3. Add your custom domain
4. Configure DNS records as instructed
