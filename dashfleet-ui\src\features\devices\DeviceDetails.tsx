// import React, { useState, useEffect } from 'react';
// import { useParams, useNavigate } from 'react-router-dom';
// import { format } from 'date-fns';
// import { 
//   ChevronLeft, Camera, Settings, List, Activity,
//   Phone, Info, MapPin, Calendar, ArrowDownCircle, 
//   FileText, Edit, Truck, AlertTriangle, RefreshCw, User
// } from 'lucide-react';

// import { Card, CardContent, CardHeader, CardTitle } from '../../components/common/Card';
// import { Button } from '../../components/common/Button';
// import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../components/common/Tabs';
// import { Spinner } from '../../components/common/Spinner';
// import { Alert } from '../../components/common/Alert';
// import { LiveVideoFeed } from '../../components/dashcam/LiveVideoFeed';
// // import { DeviceHistory } from './DeviceHistory';
// import { DeviceSettings } from './DeviceSettings';
// // import { DeviceHealthChecks } from './DeviceHealthChecks';
// import { LocationHeatmap } from '../../components/charts/LocationHeatMap';

// import { VisionMaxAPI, DeviceInfo, NewDeviceInfo } from '../../api/visionMaxApi';

// const DeviceDetails: React.FC = () => {
//   const { id } = useParams<{ id: string }>();
//   const navigate = useNavigate();
//   const [activeTab, setActiveTab] = useState('live');
  
//   const [device, setDevice] = useState<NewDeviceInfo | null>(null);
//   const [loading, setLoading] = useState(true);
//   const [error, setError] = useState<Error | null>(null);
  
//   // Create API instance
//   const token = localStorage.getItem('token');
//   const api = new VisionMaxAPI({
//     baseURL: 'https://api.visionmaxfleet.com',
//     token
//   });
  
//   // Fetch device data
//   const fetchDevice = async () => {
//     if (!id) return;
    
//     setLoading(true);
//     setError(null);
    
//     try {
//       const response = await api.getDevices({ cid: id, limit: 1 });
//       if (response.data.length > 0) {
//         setDevice(response.data[0]);
//       } else {
//         setError(new Error(`No device found with ID: ${id}`));
//       }
//     } catch (err) {
//       setError(err instanceof Error ? err : new Error('Failed to fetch device'));
//     } finally {
//       setLoading(false);
//     }
//   };
  
//   // Load device on mount
//   useEffect(() => {
//     fetchDevice();
//   }, [id]);
  
//   // Handle back button
//   const handleBack = () => {
//     navigate('/devices');
//   };
  
//   // Format date
//   const formatDate = (dateString: string) => {
//     try {
//       return format(new Date(dateString), 'MMM d, yyyy');
//     } catch (error) {
//       return dateString;
//     }
//   };
  
//   // Get health status color
//   const getHealthStatusColor = (health: string) => {
//     switch (health) {
//       case 'Healthy':
//         return 'bg-green-100 text-green-800';
//       case 'Warning':
//         return 'bg-yellow-100 text-yellow-800';
//       case 'Error':
//         return 'bg-red-100 text-red-800';
//       default:
//         return 'bg-gray-100 text-gray-800';
//     }
//   };
  
//   // Handle contact driver button
//   const handleContactDriver = () => {
//     alert('Contact driver functionality would be implemented here');
//   };
  
//   // Handle download logs button
//   const handleDownloadLogs = () => {
//     alert('Download logs functionality would be implemented here');
//   };
  
//   // Loading state
//   if (loading) {
//     return (
//       <div className="flex items-center justify-center h-96">
//         <Spinner size="large" />
//       </div>
//     );
//   }
  
//   // Error state
//   if (error) {
//     return (
//       <Alert
//         type="error"
//         title="Error Loading Device"
//         message="There was a problem loading the device data. Please try again."
//         action={{
//           label: 'Retry',
//           onClick: fetchDevice
//         }}
//       />
//     );
//   }
  
//   // Not found state
//   if (!device) {
//     return (
//       <Alert
//         type="error"
//         title="Device Not Found"
//         message={`No device found with ID: ${id}`}
//         action={{
//           label: 'Back to Devices',
//           onClick: handleBack
//         }}
//       />
//     );
//   }
  
//   // Get ADAS health status from device status
//   // const adasHealth = device.adasStatus === 'on' ? 'Healthy' : 'Error';
  
//   // Determine if device is online based on status
//   const isOnline = device.is_online;
  
//   // Extract location from activatedFrom if it exists
//   const location = device.latest_lat ? {
//     lat: device.latest_lat,
//     lng: device.latest_lng
//   } : null;
  
//   return (
//     <div className="space-y-6">
//       {/* Header with back button and actions */}
//       <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0">
//         <div className="flex items-center">
//           <Button variant="ghost" onClick={handleBack} className="mr-4">
//             <ChevronLeft size={16} className="mr-1" />
//             Back
//           </Button>
          
//           <div>
//             <h1 className="text-2xl font-bold text-gray-900">{device.name}</h1>
//             <div className="flex items-center mt-1">
//               <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
//                 isOnline ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
//               }`}>
//                 {isOnline ? 'Online' : 'Offline'}
//               </span>
//               <span className="mx-2 text-gray-300">|</span>
//               <span className="text-sm text-gray-500">Model: {device.sku_name || 'Unknown'}</span>
//               <span className="mx-2 text-gray-300">|</span>
//               <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getHealthStatusColor(isOnline ? 'Healthy' : 'Error')}`}>
//                 {isOnline}
//               </span>
//             </div>
//           </div>
//         </div>
        
//         <div className="flex space-x-3">
//           <Button 
//             variant="outline" 
//             onClick={handleContactDriver}
//             disabled={true} // Disable until we have driver info
//           >
//             <Phone size={16} className="mr-2" />
//             Contact Driver
//           </Button>
//           <Button onClick={handleDownloadLogs}>
//             <ArrowDownCircle size={16} className="mr-2" />
//             Download Logs
//           </Button>
//         </div>
//       </div>
      
//       {/* Device info cards */}
//       <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
//         <Card>
//           <CardHeader className="pb-2">
//             <CardTitle className="text-sm text-gray-500">Serial Number</CardTitle>
//           </CardHeader>
//           <CardContent>
//             <div className="text-lg font-medium">{device.serial_number}</div>
//           </CardContent>
//         </Card>
        
//         <Card>
//           <CardHeader className="pb-2">
//             <CardTitle className="text-sm text-gray-500">Activated Since</CardTitle>
//           </CardHeader>
//           <CardContent>
//             <div className="text-lg font-medium">{formatDate(device.active_at)}</div>
//           </CardContent>
//         </Card>
        
//         <Card>
//           <CardHeader className="pb-2">
//             <CardTitle className="text-sm text-gray-500">App Version</CardTitle>
//           </CardHeader>
//           <CardContent>
//             <div className="text-lg font-medium">{device.application_version}</div>
//           </CardContent>
//         </Card>
//       </div>
      
//       {/* Tabs for different views */}
//       <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
//         <TabsList className="grid grid-cols-4 w-full max-w-2xl">
//           <TabsTrigger value="live" className="flex items-center">
//             <Camera size={16} className="mr-2" />
//             Live View
//           </TabsTrigger>
//           <TabsTrigger value="history" className="flex items-center">
//             <List size={16} className="mr-2" />
//             Event History
//           </TabsTrigger>
//           <TabsTrigger value="health" className="flex items-center">
//             <Activity size={16} className="mr-2" />
//             Health
//           </TabsTrigger>
//           <TabsTrigger value="settings" className="flex items-center">
//             <Settings size={16} className="mr-2" />
//             Settings
//           </TabsTrigger>
//         </TabsList>
        
//         <TabsContent value="live" className="mt-6">
//           <Card>
//             <CardContent className="p-0">
//               <LiveVideoFeed 
//                 deviceId={id!} 
//                 isOnline={isOnline} 
//               />
//             </CardContent>
//           </Card>
          
//           <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
//             <Card>
//               <CardHeader>
//                 <CardTitle className="flex items-center">
//                   <Info size={20} className="mr-2" />
//                   Current Status
//                 </CardTitle>
//               </CardHeader>
//               <CardContent>
//                 <div className="space-y-4">
//                   <div className="grid grid-cols-2 gap-4">
//                     <div>
//                       <div className="text-sm text-gray-500">Connection</div>
//                       <div className="text-lg font-medium">
//                         {isOnline ? (
//                           <span className="text-green-600">Connected</span>
//                         ) : (
//                           <span className="text-gray-500">Disconnected</span>
//                         )}
//                       </div>
//                     </div>
//                     <div>
//                       <div className="text-sm text-gray-500">Signal Strength</div>
//                       <div className="text-lg font-medium">
//                         {isOnline ? '75%' : 'N/A'}
//                       </div>
//                     </div>
//                     <div>
//                       <div className="text-sm text-gray-500">Battery</div>
//                       <div className="text-lg font-medium">
//                         {isOnline ? '90%' : 'N/A'}
//                       </div>
//                     </div>
//                     <div>
//                       <div className="text-sm text-gray-500">Storage</div>
//                       <div className="text-lg font-medium">
//                         {isOnline ? '32.5 GB free' : 'N/A'}
//                       </div>
//                     </div>
//                   </div>
                  
//                   {isOnline && (
//                     <div className="text-sm text-gray-500 mt-2">
//                       Last updated: {format(new Date(), 'MMM d, yyyy h:mm:ss a')}
//                       <Button 
//                         variant="ghost" 
//                         size="sm" 
//                         className="ml-2 p-1 h-6"
//                         onClick={fetchDevice}
//                       >
//                         <RefreshCw size={14} />
//                       </Button>
//                     </div>
//                   )}
//                 </div>
//               </CardContent>
//             </Card>
            
//             <Card>
//               <CardHeader>
//                 <CardTitle className="flex items-center">
//                   <MapPin size={20} className="mr-2" />
//                   Location
//                 </CardTitle>
//               </CardHeader>
//               <CardContent className="p-0 h-48">
//                 {isOnline && location ? (
//                   <LocationHeatmap
//                     points={[location]}
//                     zoom={14}
//                     showControls={false}
//                     height="h-48"
//                   />
//                 ) : (
//                   <div className="w-full h-full flex items-center justify-center bg-gray-100">
//                     <span className="text-gray-500">Location data unavailable</span>
//                   </div>
//                 )}
//               </CardContent>
//             </Card>
//           </div>
          
//           {/* Driver info would be added here if available */}
//         </TabsContent>
        
//         <TabsContent value="history" className="mt-6">
//           <DeviceHistory deviceId={id!} />
//         </TabsContent>
        
//         <TabsContent value="health" className="mt-6">
//           <DeviceHealthChecks deviceId={id!} health={isOnline? "Healthy" :"Error"} />
//         </TabsContent>
        
//         <TabsContent value="settings" className="mt-6">
//           <DeviceSettings 
//             device={device}
//             onUpdate={(updates: any) => {
//               alert('Device update functionality would be implemented here');
//               console.log('Updates:', updates);
//             }}
//             isUpdating={false}
//           />
//         </TabsContent>
//       </Tabs>
//     </div>
//   );
// };

// export default DeviceDetails;