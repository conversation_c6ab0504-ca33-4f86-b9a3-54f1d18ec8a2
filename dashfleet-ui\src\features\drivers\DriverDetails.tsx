import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, useNavigate } from 'react-router-dom';
import { format } from 'date-fns';
import {
  ChevronLeft, User, Phone, Mail, Truck, Calendar,
  FileText, MapPin, BarChart2, Edit, UserX, Download,
  AlertTriangle, Clock
} from 'lucide-react';

import { Card, CardContent, CardHeader, CardTitle } from '../../components/common/Card';
import { Button } from '../../components/common/Button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../components/common/Tabs';
import { Spinner } from '../../components/common/Spinner';
import { Alert } from '../../components/common/Alert';
import { TripStatsChart } from '../../components/charts/TripStatsChart';
import { EventsDistributionChart, SpeedChart } from '../../components/charts/SpeedChart';
import { DriverDocuments } from './DriverDocuments';
import { DriverVehicleHistory } from './DriverVehicleHistory';
import { TripsTable } from '../trips/TripsTable';
import { VisionMaxAPI } from '../../api/visionMaxApi';

// Create a mock implementation for driver details since the API doesn't provide a specific endpoint
interface DriverDetail {
  id: number;
  name: string;
  employee_id: string;
  email: string;
  phone: string;
  rfid: string;
  avatar: string;
  status: string;
  department?: string;
  joinDate?: string;
  licenseType?: string;
  licenseNumber?: string;
  licenseExpiry?: string;
  safetyRating?: number;
  notes?: string;
  emergencyContact?: {
    name: string;
    phone: string;
  };
  assignedVehicle?: {
    name: string;
    type: string;
    licensePlate: string;
  };
  stats?: {
    totalTrips?: number;
    totalDistance?: number;
    totalDrivingTime?: number;
    speedCompliance?: number;
    idleTimePercentage?: number;
    fuelEfficiency?: number;
    onTimeDelivery?: number;
    monthlyActivity?: any[];
    safetyScoreTrend?: any[];
    eventDistribution?: {
      hardBraking: number;
      speeding: number;
      hardAcceleration: number;
      harshTurn: number;
      other: number;
    };
  };
  recentActivity?: any[];
  trips?: any[];
}

const DriverDetails: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('overview');

  // API state
  const [api] = useState(() => {
    // You would get this from your auth context or similar
    return new VisionMaxAPI({
      baseURL: import.meta.env.VITE_APP_API_BASE_URL?.replace('/V2', '') || 'https://api.visionmaxfleet.com',
      token: localStorage.getItem('auth_token') || undefined
    });
  });

  // Driver state
  const [driver, setDriver] = useState<DriverDetail | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  // Fetch driver data
  const fetchDriverData = async () => {
    if (!id) return;

    try {
      setIsLoading(true);
      setError(null);

      // Since there's no specific driver detail endpoint, we'll use getAllDrivers
      // and filter for the specific driver by ID in a real app.
      // This is just a mock implementation.
      const allDriversResponse = await api.getAllDrivers({});

      if (allDriversResponse.result) {
        const driverData = allDriversResponse.data.find(
          d => d.id === parseInt(id)
        );
      } else {
        throw new Error(allDriversResponse.message);
      }
    } catch (err) {
      setError(err instanceof Error ? err : new Error('An unknown error occurred'));
    } finally {
      setIsLoading(false);
    }
  };

  // Generate mock data for UI rendering
  function generateMockMonthlyActivity() {
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'];
    return months.map(month => ({
      month,
      trips: Math.floor(Math.random() * 20) + 5,
      distance: Math.floor(Math.random() * 1000) + 200,
      time: Math.floor(Math.random() * 30) + 10
    }));
  }

  function generateMockSafetyTrend() {
    const days = Array.from({ length: 30 }, (_, i) => {
      const date = new Date();
      date.setDate(date.getDate() - i);
      return format(date, 'MMM dd');
    }).reverse();

    return days.map(date => ({
      date,
      score: 3 + Math.random() * 2 // Score between 3-5
    }));
  }

  function generateMockRecentActivity() {
    const activityTypes = ['trip', 'event', 'document'];
    const dates = [1, 2, 3, 4, 5].map(day => {
      const date = new Date();
      date.setDate(date.getDate() - day);
      return format(date, 'yyyy-MM-dd');
    });

    return [
      {
        type: 'trip',
        title: 'Completed delivery route',
        timestamp: dates[0],
        description: 'Delivered to 12 destinations in the downtown area.'
      },
      {
        type: 'event',
        title: 'Harsh braking event',
        timestamp: dates[1],
        description: 'Harsh braking detected due to pedestrian crossing.'
      },
      {
        type: 'document',
        title: 'License renewal uploaded',
        timestamp: dates[2],
        description: 'Commercial driver license renewed through 2025.'
      },
      {
        type: 'trip',
        title: 'Long-haul route completed',
        timestamp: dates[3],
        description: 'Interstate delivery completed 2 hours ahead of schedule.'
      },
      {
        type: 'event',
        title: 'Maintenance check',
        timestamp: dates[4],
        description: 'Scheduled vehicle maintenance performed.'
      }
    ];
  }

  function generateMockTrips() {
    return Array.from({ length: 5 }, (_, i) => {
      const date = new Date();
      date.setDate(date.getDate() - i * 2);

      return {
        id: 1000 + i,
        startTime: format(date, 'yyyy-MM-dd HH:mm:ss'),
        endTime: format(new Date(date.getTime() + 5 * 60 * 60 * 1000), 'yyyy-MM-dd HH:mm:ss'),
        distance: Math.floor(Math.random() * 100) + 20,
        duration: Math.floor(Math.random() * 5) + 2,
        status: 'completed',
        origin: 'Warehouse',
        destination: `Customer Site ${i + 1}`,
        vehicle: `Truck ${300 + i}`,
        events: Math.floor(Math.random() * 3)
      };
    });
  }

  // Fetch driver on component mount
  useEffect(() => {
    fetchDriverData();
  }, [id]);

  // Handle back button
  const handleBack = () => {
    navigate('/drivers');
  };

  // Handle edit driver
  const handleEditDriver = () => {
    navigate(`/drivers/${id}/edit`);
  };

  // Refetch data
  const refetch = () => {
    fetchDriverData();
  };

  // Format date
  const formatDate = (dateString?: string) => {
    if (!dateString) return 'N/A';
    try {
      return format(new Date(dateString), 'MMM d, yyyy');
    } catch (error) {
      return dateString;
    }
  };

  // Get status badge color
  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'inactive':
        return 'bg-gray-100 text-gray-800';
      case 'onLeave':
        return 'bg-yellow-100 text-yellow-800';
      case 'suspended':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-96">
        <Spinner size="large" />
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <Alert
        type="error"
        title="Error Loading Driver"
        message={error.message || "There was a problem loading the driver data. Please try again."}
        action={{
          label: 'Retry',
          onClick: refetch
        }}
      />
    );
  }

  // Not found state
  if (!driver) {
    return (
      <Alert
        type="error"
        title="Driver Not Found"
        message={`No driver found with ID: ${id}`}
        action={{
          label: 'Back to Drivers',
          onClick: handleBack
        }}
      />
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with back button and actions */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0">
        <div className="flex items-center">
          <Button variant="ghost" onClick={handleBack} className="mr-4">
            <ChevronLeft size={16} className="mr-1" />
            Back
          </Button>

          <div className="flex items-center">
            <div className="h-12 w-12 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 overflow-hidden mr-4">
              {driver.avatar ? (
                <img src={driver.avatar} alt={driver.name} className="h-full w-full object-cover" />
              ) : (
                <User size={24} />
              )}
            </div>

            <div>
              <h1 className="text-2xl font-bold text-gray-900">{driver.name}</h1>
              <div className="flex items-center mt-1">
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusBadgeColor(driver.status)}`}>
                  {driver.status.charAt(0).toUpperCase() + driver.status.slice(1)}
                </span>
                <span className="mx-2 text-gray-300">|</span>
                <span className="text-sm text-gray-500">ID: {driver.id}</span>
              </div>
            </div>
          </div>
        </div>

        <div className="flex space-x-3">
          <Button variant="outline" onClick={handleEditDriver} leftIcon={<Edit size={16} />}>
            Edit
          </Button>
          <Button
            variant="outline"
            className="text-red-600 border-red-300 hover:bg-red-50"
            leftIcon={<UserX size={16} />}
          >
            Deactivate
          </Button>
        </div>
      </div>

      {/* Driver info cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm text-gray-500 flex items-center">
              <Phone size={16} className="mr-2" />
              Contact Information
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div>
                <div className="text-xs text-gray-500">Phone</div>
                <div className="text-sm font-medium">{driver.phone}</div>
              </div>
              <div>
                <div className="text-xs text-gray-500">Email</div>
                <div className="text-sm font-medium">{driver.email}</div>
              </div>
              <div>
                <div className="text-xs text-gray-500">Emergency Contact</div>
                <div className="text-sm font-medium">{driver.emergencyContact?.name || 'Not provided'}</div>
                <div className="text-xs text-gray-500">{driver.emergencyContact?.phone || ''}</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm text-gray-500 flex items-center">
              <Truck size={16} className="mr-2" />
              Vehicle Information
            </CardTitle>
          </CardHeader>
          <CardContent>
            {driver.assignedVehicle ? (
              <div className="space-y-2">
                <div>
                  <div className="text-xs text-gray-500">Vehicle</div>
                  <div className="text-sm font-medium">{driver.assignedVehicle.name}</div>
                </div>
                <div>
                  <div className="text-xs text-gray-500">Type</div>
                  <div className="text-sm font-medium">{driver.assignedVehicle.type}</div>
                </div>
                <div>
                  <div className="text-xs text-gray-500">License Plate</div>
                  <div className="text-sm font-medium">{driver.assignedVehicle.licensePlate}</div>
                </div>
              </div>
            ) : (
              <div className="flex items-center justify-center h-16">
                <span className="text-gray-500">No vehicle assigned</span>
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm text-gray-500 flex items-center">
              <FileText size={16} className="mr-2" />
              License Information
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div>
                <div className="text-xs text-gray-500">License Type</div>
                <div className="text-sm font-medium">{driver.licenseType}</div>
              </div>
              <div>
                <div className="text-xs text-gray-500">License Number</div>
                <div className="text-sm font-medium">{driver.licenseNumber}</div>
              </div>
              <div>
                <div className="text-xs text-gray-500">Expiry Date</div>
                <div className="text-sm font-medium">{formatDate(driver.licenseExpiry)}</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm text-gray-500 flex items-center">
              <Calendar size={16} className="mr-2" />
              Employment Information
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div>
                <div className="text-xs text-gray-500">Join Date</div>
                <div className="text-sm font-medium">{formatDate(driver.joinDate)}</div>
              </div>
              <div>
                <div className="text-xs text-gray-500">Employee ID</div>
                <div className="text-sm font-medium">{driver.employee_id || 'N/A'}</div>
              </div>
              <div>
                <div className="text-xs text-gray-500">Department</div>
                <div className="text-sm font-medium">{driver.department || 'N/A'}</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Tabs for different views */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid grid-cols-5 w-full max-w-3xl">
          <TabsTrigger value="overview" className="flex items-center">
            <User size={16} className="mr-2" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="trips" className="flex items-center">
            <MapPin size={16} className="mr-2" />
            Trips
          </TabsTrigger>
          <TabsTrigger value="performance" className="flex items-center">
            <BarChart2 size={16} className="mr-2" />
            Performance
          </TabsTrigger>
          <TabsTrigger value="vehicles" className="flex items-center">
            <Truck size={16} className="mr-2" />
            Vehicles
          </TabsTrigger>
          <TabsTrigger value="documents" className="flex items-center">
            <FileText size={16} className="mr-2" />
            Documents
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="mt-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Driver Summary</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <div className="text-sm text-gray-500">Total Trips</div>
                      <div className="text-2xl font-bold">{driver.stats?.totalTrips || 0}</div>
                    </div>
                    <div>
                      <div className="text-sm text-gray-500">Total Distance</div>
                      <div className="text-2xl font-bold">{driver.stats?.totalDistance?.toLocaleString() || 0} km</div>
                    </div>
                    <div>
                      <div className="text-sm text-gray-500">Avg Trip Length</div>
                      <div className="text-2xl font-bold">
                        {driver.stats?.totalTrips
                          ? (driver.stats.totalDistance / driver.stats.totalTrips).toFixed(2)
                          : 0} km
                      </div>
                    </div>
                    <div>
                      <div className="text-sm text-gray-500">Total Driving Time</div>
                      <div className="text-2xl font-bold">{driver.stats?.totalDrivingTime?.toFixed(1) || 0} hrs</div>
                    </div>
                  </div>

                  <div className="pt-4 border-t">
                    <div className="text-sm font-medium mb-2">Safety Rating</div>
                    <div className="flex items-center">
                      <div className="flex-1 bg-gray-200 rounded-full h-2">
                        <div
                          className={`h-2 rounded-full ${
                            driver.safetyRating >= 4.5 ? 'bg-green-500' :
                            driver.safetyRating >= 3.5 ? 'bg-blue-500' :
                            driver.safetyRating >= 2.5 ? 'bg-yellow-500' :
                            'bg-red-500'
                          }`}
                          style={{ width: `${(driver.safetyRating / 5) * 100}%` }}
                        />
                      </div>
                      <div className="ml-4 text-xl font-bold">
                        {driver.safetyRating?.toFixed(1) || 'N/A'}
                      </div>
                    </div>
                  </div>

                  <div className="pt-4 border-t">
                    <div className="text-sm font-medium mb-2">Notes</div>
                    <p className="text-sm text-gray-700">
                      {driver.notes || 'No notes available for this driver.'}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Recent Activity</CardTitle>
              </CardHeader>
              <CardContent>
                {driver.recentActivity && driver.recentActivity.length > 0 ? (
                  <div className="space-y-4">
                    {driver.recentActivity.map((activity, index) => (
                      <div key={index} className="flex">
                        <div
                          className={`w-10 h-10 rounded-full flex items-center justify-center ${
                            activity.type === 'trip' ? 'bg-blue-100 text-blue-600' :
                            activity.type === 'event' ? 'bg-yellow-100 text-yellow-600' :
                            activity.type === 'document' ? 'bg-purple-100 text-purple-600' :
                            'bg-gray-100 text-gray-600'
                          }`}
                        >
                          {activity.type === 'trip' ? <MapPin size={18} /> :
                           activity.type === 'event' ? <AlertTriangle size={18} /> :
                           activity.type === 'document' ? <FileText size={18} /> :
                           <Clock size={18} />}
                        </div>
                        <div className="ml-4 flex-1">
                          <div className="text-sm font-medium">{activity.title}</div>
                          <div className="text-xs text-gray-500">{formatDate(activity.timestamp)}</div>
                          <div className="text-sm text-gray-700 mt-1">{activity.description}</div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="flex items-center justify-center h-48 text-gray-500">
                    No recent activity available
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {driver.stats?.monthlyActivity && (
            <Card className="mt-6">
              <CardHeader>
                <CardTitle>Monthly Activity</CardTitle>
              </CardHeader>
              <CardContent>
                <TripStatsChart data={driver.stats.monthlyActivity} />
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="trips" className="mt-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle>Recent Trips</CardTitle>
              <Button variant="outline" leftIcon={<Download size={16} />}>
                Export Trips
              </Button>
            </CardHeader>
            <CardContent className="p-0">
              {driver.trips && driver.trips.length > 0 ? (
                <TripsTable
                  trips={driver.trips}
                  isLoading={false}
                  onRowClick={(tripId) => navigate(`/trips/${tripId}`)}
                />
              ) : (
                <div className="flex items-center justify-center h-48 text-gray-500">
                  No trips available for this driver
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="performance" className="mt-6">
          <div className="grid grid-cols-1 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Safety Score Trends</CardTitle>
              </CardHeader>
              <CardContent>
                {driver.stats?.safetyScoreTrend ? (
                  <div className="h-80">
                    <SpeedChart
                      data={driver.stats.safetyScoreTrend.map((item) => ({
                        time: item.date,
                        speed: item.score * 20, // Convert 0-5 score to 0-100 for chart
                      }))}
                      speedLimit={80} // 4.0 score threshold
                      showAcceleration={false}
                    />
                  </div>
                ) : (
                  <div className="flex items-center justify-center h-48 text-gray-500">
                    No safety score data available
                  </div>
                )}
              </CardContent>
            </Card>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Event Distribution</CardTitle>
                </CardHeader>
                <CardContent>
                  {driver.stats?.eventDistribution ? (
                    <EventsDistributionChart
                      data={[
                        {
                          type: 'Hard Braking',
                          count: driver.stats.eventDistribution.hardBraking,
                          color: '#EF4444',
                        },
                        {
                          type: 'Speeding',
                          count: driver.stats.eventDistribution.speeding,
                          color: '#F97316',
                        },
                        {
                          type: 'Hard Acceleration',
                          count: driver.stats.eventDistribution.hardAcceleration,
                          color: '#F59E0B',
                        },
                        {
                          type: 'Harsh Turn',
                          count: driver.stats.eventDistribution.harshTurn,
                          color: '#10B981',
                        },
                        {
                          type: 'Other',
                          count: driver.stats.eventDistribution.other,
                          color: '#6B7280',
                        },
                      ]}
                    />
                  ) : (
                    <div className="flex items-center justify-center h-48 text-gray-500">
                      No event data available
                    </div>
                  )}
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Performance Metrics</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <div className="flex justify-between items-center mb-1">
                        <div className="text-sm font-medium">Speed Compliance</div>
                        <div className="text-sm font-medium">
                          {driver.stats?.speedCompliance?.toFixed(1) || 'N/A'}%
                        </div>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-blue-600 h-2 rounded-full"
                          style={{ width: `${driver.stats?.speedCompliance || 0}%` }}
                        />
                      </div>
                    </div>

                    <div>
                      <div className="flex justify-between items-center mb-1">
                        <div className="text-sm font-medium">Idle Time</div>
                        <div className="text-sm font-medium">
                          {driver.stats?.idleTimePercentage?.toFixed(1) || 'N/A'}%
                        </div>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-yellow-600 h-2 rounded-full"
                          style={{ width: `${driver.stats?.idleTimePercentage || 0}%` }}
                        />
                      </div>
                    </div>

                    <div>
                      <div className="flex justify-between items-center mb-1">
                        <div className="text-sm font-medium">Fuel Efficiency</div>
                        <div className="text-sm font-medium">
                          {driver.stats?.fuelEfficiency?.toFixed(1) || 'N/A'} km/L
                        </div>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-green-600 h-2 rounded-full"
                          style={{ width: `${(driver.stats?.fuelEfficiency || 0) * 5}%` }}
                        />
                      </div>
                    </div>

                    <div>
                      <div className="flex justify-between items-center mb-1">
                        <div className="text-sm font-medium">On-Time Delivery</div>
                        <div className="text-sm font-medium">
                          {driver.stats?.onTimeDelivery?.toFixed(1) || 'N/A'}%
                        </div>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-purple-600 h-2 rounded-full"
                          style={{ width: `${driver.stats?.onTimeDelivery || 0}%` }}
                        />
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="vehicles" className="mt-6">
          <DriverVehicleHistory driverId={id!} />
        </TabsContent>

        <TabsContent value="documents" className="mt-6">
          <DriverDocuments driverId={id!} />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default DriverDetails;