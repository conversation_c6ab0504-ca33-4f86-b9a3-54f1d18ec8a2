import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { format } from 'date-fns';
import { Truck, Calendar } from 'lucide-react';

import { Card, CardContent, CardHeader, CardTitle } from '../../components/common/Card';
import { Button } from '../../components/common/Button';
import { Spinner } from '../../components/common/Spinner';
import { Alert } from '../../components/common/Alert';
import { VisionMaxAPI } from '../../api/visionMaxApi';

interface VehicleAssignment {
  id: string;
  vehicleId: string;
  vehicleName: string;
  vehicleType: string;
  licensePlate: string;
  startDate: string;
  endDate?: string;
  isCurrentlyAssigned: boolean;
  notes?: string;
}

interface DriverVehicleHistoryProps {
  driverId: string;
}

export const DriverVehicleHistory: React.FC<DriverVehicleHistoryProps> = ({ driverId }) => {
  const navigate = useNavigate();

  // API state
  const [api] = useState(() => {
    // You would get this from your auth context or similar
    return new VisionMaxAPI({
      baseURL: import.meta.env.VITE_APP_API_BASE_URL?.replace('/V2', '') || 'https://api.visionmaxfleet.com',
      token: localStorage.getItem('auth_token') || undefined
    });
  });

  // Vehicle history state
  const [vehicleHistory, setVehicleHistory] = useState<{
    currentAssignment?: VehicleAssignment;
    assignmentHistory: VehicleAssignment[];
  } | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  // Fetch vehicle history - since there's no API endpoint for driver vehicle history, this is a mock implementation
  const fetchVehicleHistory = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // In a real implementation, we would call an API endpoint like:
      // const response = await api.getDriverVehicleHistory(driverId);

      // Simulate network request
      await new Promise(resolve => setTimeout(resolve, 1000));

      // For now, we'll use mock data
      const currentAssignment: VehicleAssignment = {
        id: '1',
        vehicleId: '101',
        vehicleName: 'Truck 347',
        vehicleType: 'Semi-Trailer',
        licensePlate: 'ABC1234',
        startDate: '2023-05-15',
        isCurrentlyAssigned: true,
        notes: 'Primary vehicle for long-haul routes. Regular maintenance scheduled for every 10,000 miles.'
      };

      const assignmentHistory: VehicleAssignment[] = [
        {
          ...currentAssignment
        },
        {
          id: '2',
          vehicleId: '102',
          vehicleName: 'Truck 285',
          vehicleType: 'Box Truck',
          licensePlate: 'DEF5678',
          startDate: '2022-11-20',
          endDate: '2023-05-14',
          isCurrentlyAssigned: false
        },
        {
          id: '3',
          vehicleId: '103',
          vehicleName: 'Van 156',
          vehicleType: 'Cargo Van',
          licensePlate: 'GHI9012',
          startDate: '2022-06-10',
          endDate: '2022-11-19',
          isCurrentlyAssigned: false
        },
        {
          id: '4',
          vehicleId: '104',
          vehicleName: 'Truck 193',
          vehicleType: 'Flatbed',
          licensePlate: 'JKL3456',
          startDate: '2021-12-05',
          endDate: '2022-06-09',
          isCurrentlyAssigned: false,
          notes: 'Vehicle reassigned due to maintenance issues.'
        }
      ];

      setVehicleHistory({
        currentAssignment,
        assignmentHistory
      });
    } catch (err) {
      setError(err instanceof Error ? err : new Error('An unknown error occurred'));
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch vehicle history on component mount
  useEffect(() => {
    fetchVehicleHistory();
  }, [driverId]);

  // Format date
  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Present';
    try {
      return format(new Date(dateString), 'MMM d, yyyy');
    } catch (error) {
      return dateString;
    }
  };

  // Handle assign vehicle
  const handleAssignVehicle = () => {
    alert('Vehicle assignment functionality would be implemented here');
  };

  // Refetch data
  const refetch = () => {
    fetchVehicleHistory();
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="flex justify-center items-center p-8">
        <Spinner size="large" color="primary" />
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <Alert
        type="error"
        title="Error Loading Vehicle History"
        message={error.message || "There was a problem loading the driver's vehicle history. Please try again."}
        action={{
          label: 'Retry',
          onClick: refetch
        }}
      />
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-lg font-medium">Vehicle Assignments</h2>
        <Button leftIcon={<Truck size={16} />} onClick={handleAssignVehicle}>
          Assign Vehicle
        </Button>
      </div>

      {/* Current assignment */}
      <Card>
        <CardHeader>
          <CardTitle>Current Assignment</CardTitle>
        </CardHeader>
        <CardContent>
          {vehicleHistory?.currentAssignment ? (
            <div className="flex items-start">
              <div className="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center text-blue-600 mr-4">
                <Truck size={32} />
              </div>
              <div className="flex-1">
                <h3 className="text-lg font-medium">{vehicleHistory.currentAssignment.vehicleName}</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
                  <div>
                    <div className="text-sm text-gray-500">Vehicle Type</div>
                    <div className="text-sm font-medium">{vehicleHistory.currentAssignment.vehicleType}</div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-500">License Plate</div>
                    <div className="text-sm font-medium">{vehicleHistory.currentAssignment.licensePlate}</div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-500">Assigned Since</div>
                    <div className="text-sm font-medium">{formatDate(vehicleHistory.currentAssignment.startDate)}</div>
                  </div>
                  <div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => navigate(`/vehicles/${vehicleHistory.currentAssignment.vehicleId}`)}
                    >
                      View Vehicle
                    </Button>
                  </div>
                </div>
                {vehicleHistory.currentAssignment.notes && (
                  <div className="mt-4 p-3 bg-gray-50 rounded-md text-sm">
                    <div className="font-medium mb-1">Notes:</div>
                    <div>{vehicleHistory.currentAssignment.notes}</div>
                  </div>
                )}
              </div>
            </div>
          ) : (
            <div className="py-8 text-center text-gray-500">
              No vehicle currently assigned to this driver
            </div>
          )}
        </CardContent>
      </Card>

      {/* Assignment history */}
      <Card>
        <CardHeader>
          <CardTitle>Assignment History</CardTitle>
        </CardHeader>
        <CardContent>
          {vehicleHistory?.assignmentHistory && vehicleHistory.assignmentHistory.length > 0 ? (
            <div className="space-y-6">
              {vehicleHistory.assignmentHistory.map((assignment) => (
                <div key={assignment.id} className={`p-4 rounded-lg border ${
                  assignment.isCurrentlyAssigned ? 'border-blue-200 bg-blue-50' : 'border-gray-200'
                }`}>
                  <div className="flex items-start">
                    <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                      assignment.isCurrentlyAssigned ? 'bg-blue-100 text-blue-600' : 'bg-gray-100 text-gray-600'
                    }`}>
                      <Truck size={20} />
                    </div>
                    <div className="ml-4 flex-1">
                      <div className="flex justify-between">
                        <div>
                          <div className="text-lg font-medium">{assignment.vehicleName}</div>
                          <div className="text-sm text-gray-500">License Plate: {assignment.licensePlate}</div>
                        </div>
                        {assignment.isCurrentlyAssigned && (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            Current
                          </span>
                        )}
                      </div>
                      <div className="mt-2 flex items-center text-sm text-gray-500">
                        <Calendar size={16} className="mr-1" />
                        <span>
                          Assigned: {formatDate(assignment.startDate)}
                          {assignment.endDate ? ` • Ended: ${formatDate(assignment.endDate)}` : ' • Current'}
                        </span>
                      </div>
                      {assignment.notes && (
                        <div className="mt-2 text-sm text-gray-700">
                          <span className="font-medium">Notes:</span> {assignment.notes}
                        </div>
                      )}
                      {!assignment.isCurrentlyAssigned && (
                        <div className="mt-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => navigate(`/vehicles/${assignment.vehicleId}`)}
                          >
                            View Vehicle
                          </Button>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="py-8 text-center text-gray-500">
              No previous vehicle assignments found
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};