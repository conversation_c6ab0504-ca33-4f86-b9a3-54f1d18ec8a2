import React, { useEffect, useRef, useState } from 'react';
import { Camera, Volume2, VolumeX, AlertTriangle, RefreshCw } from 'lucide-react';


declare global {
  interface Window {
    vmRTC: {
      addEventListener: (event: string, callback: (data?: any) => void) => void;
      setup: (config: any) => typeof window.vmRTC;
      start: (cid: string) => void;
      stop: () => void;
      nextCam: () => void;
    },
  sessionTimerInterval?: NodeJS.Timeout;      
  }
}

// Updated component to accept both credential formats
const VisionMaxLiveView = ({ 
  device, 
  // Original props


  title,
  // Common props
  onClose, 
  onError,
  modalStyle = true 
}) => {
  const videoRef = useRef(null);
  const [isConnecting, setIsConnecting] = useState(true);
  const [connectionError, setConnectionError] = useState(null);
  const [connectionProgress, setConnectionProgress] = useState(0);
  const [isMuted, setIsMuted] = useState(true);
  const [remainingTime, setRemainingTime] = useState(null);
  // Extract API key and access token from credentials if provided
  
  // Check if required props are provided
  useEffect(() => {
    let effectiveApiKey =  'WnbpXF1lJ1vKePo1C5pCXbgVGbkwehE8wMvX5LV9ikQ';

    if (!device || !device.cid) {
      setConnectionError("Missing device information");
      setIsConnecting(false);
      if (onError) onError(new Error("Missing device CID"));
      return;
    }
    
    if (!effectiveApiKey) {
      setConnectionError("Missing API key");
      setIsConnecting(false);
      if (onError) onError(new Error("Missing API key"));
      return;
    }
    
    // if (!effectiveAccessToken) {
    //   setConnectionError("Missing access token");
    //   setIsConnecting(false);
    //   if (onError) onError(new Error("Missing access token"));
    //   return;
    // }
    
    // Check if vmRTC is available
    if (!window.vmRTC) {
      setConnectionError("VisionMax RTC library not available");
      setIsConnecting(false);
      if (onError) onError(new Error("vmRTC not loaded"));
      return;
    }
    
    // Initialize and set up connections
    initializeConnection();
    
    // Cleanup on unmount
    return () => {
      cleanupConnection();
    };
  }, [device]);
  
  const initializeConnection = () => {
    try {
      console.log('Initializing vmRTC with device:', device.cid);
      
      // Set up event listeners
      window.vmRTC.addEventListener("progressStatusUpdated", (percentage) => {
        console.log('Progress updated:', percentage);
        setConnectionProgress(parseInt(percentage, 10));
      });
      
      window.vmRTC.addEventListener("connect fail", (errorMsg) => {
        console.error('Connection failed:', errorMsg);
        setIsConnecting(false);
        setConnectionError("Failed to connect to the device. Please try again.");
        if (onError) onError(new Error("Connection failure"));
      });
      
      window.vmRTC.addEventListener("initial.fail", (errorMsg) => {
        console.error('Initialization failed:', errorMsg);
        setIsConnecting(false);
        setConnectionError(`Failed to initialize the connection: ${errorMsg}`);
        if (onError) onError(new Error(`Initialization failure: ${errorMsg}`));
      });
      
      window.vmRTC.addEventListener("connection lost", () => {
        console.error('Connection lost');
        setIsConnecting(false);
        setConnectionError("Connection to the device was lost.");
        if (onError) onError(new Error("Connection lost"));
      });
      
      window.vmRTC.addEventListener("peer connected", () => {
        console.log('Peer connected');
        setIsConnecting(false);
        setConnectionError(null);
      });
      
      window.vmRTC.addEventListener("session stable", () => {
        console.log('Session stable');
        startSessionTimer();
      });
      
      // Make sure video element has the required ID
      if (videoRef.current) {
        videoRef.current.id = "rtc_liveview";
      }
      
      // // Initialize vmRTC with configuration
      // console.log('Setting up vmRTC with config:', {
      //   env: 'staging',
      //   apiKey: effectiveApiKey,
      //   clientAccessToken: effectiveAccessToken,
      //   requestId: requestId,  // Add requestId if available
      //   feature: { enableDefaultCameraSwitchButton: false }
      // });
      
      // window.vmRTC.setup({
      //   env: 'staging',
      //   apiKey: effectiveApiKey,
      //   clientAccessToken: effectiveAccessToken,
      //   requestId: requestId,  // Add requestId if available
      //   feature: { enableDefaultCameraSwitchButton: false }
      // });
      
      // Start connection
      console.log('Starting vmRTC with device CID:', device.cid);
      window.vmRTC.start(device.cid);
      
    } catch (error) {
      console.error('Error initializing connection:', error);
      setIsConnecting(false);
      setConnectionError(`Error initializing connection: ${error.message}`);
      if (onError) onError(error);
    }
  };
  
  const cleanupConnection = () => {
    try {
      // Stop current stream
      if (window.vmRTC) {
        console.log('Stopping vmRTC');
        window.vmRTC.stop();
      }
      
      // Clear any timers
      if (window.sessionTimerInterval) {
        clearInterval(window.sessionTimerInterval);
        window.sessionTimerInterval = null;
      }
      
    } catch (error) {
      console.error('Error during cleanup:', error);
    }
  };
  
  const startSessionTimer = () => {
    let seconds = 600; // 10 minutes
    
    if (window.sessionTimerInterval) {
      clearInterval(window.sessionTimerInterval);
    }
    
    const updateTimer = () => {
      const minutes = Math.floor(seconds / 60);
      const remainingSeconds = seconds % 60;
      const timeString = `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
      setRemainingTime(timeString);
    };
    
    updateTimer();
    
    window.sessionTimerInterval = setInterval(() => {
      seconds--;
      updateTimer();
      
      if (seconds <= 0) {
        clearInterval(window.sessionTimerInterval);
        if (onClose) onClose();
      }
    }, 1000);
  };
  
  const handleSwitchCamera = () => {
    if (window.vmRTC) {
      try {
        console.log('Switching camera');
        window.vmRTC.nextCam();
      } catch (error) {
        console.error('Error switching camera:', error);
      }
    }
  };
  
  const toggleMute = () => {
    if (videoRef.current && videoRef.current.srcObject) {
      const stream = videoRef.current.srcObject;
      const audioTracks = stream.getAudioTracks();
      
      if (audioTracks.length > 0) {
        const newMutedState = !isMuted;
        audioTracks.forEach(track => {
          track.enabled = !newMutedState;
        });
        setIsMuted(newMutedState);
      }
    }
  };
  
  const handleSnapshot = () => {
    if (videoRef.current && videoRef.current.readyState >= videoRef.current.HAVE_CURRENT_DATA) {
      const canvas = document.createElement('canvas');
      canvas.width = videoRef.current.videoWidth;
      canvas.height = videoRef.current.videoHeight;
      
      const ctx = canvas.getContext('2d');
      if (ctx) {
        ctx.drawImage(videoRef.current, 0, 0, canvas.width, canvas.height);
        const dataUrl = canvas.toDataURL('image/png');
        
        const link = document.createElement('a');
        link.href = dataUrl;
        const deviceId = device?.id || device?.serialNumber || 'unknown';
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        link.download = `liveview_${deviceId}_${timestamp}.png`;
        
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }
    }
  };
  
  const handleClose = () => {
    cleanupConnection();
    if (onClose) onClose();
  };
  
  const handleRetry = () => {
    setIsConnecting(true);
    setConnectionError(null);
    cleanupConnection();
    
    // Short delay before retry
    setTimeout(() => {
      initializeConnection();
    }, 1000);
  };
  
  // Use the provided title or generate one
  const displayTitle = title || `Device Live View`;
  
  // Modal-style UI
  if (modalStyle) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-75 backdrop-blur-sm">
        <div className="bg-white rounded-lg shadow-xl overflow-hidden max-w-4xl w-full mx-4 flex flex-col" style={{ maxHeight: '90vh' }}>
          {/* Header */}
          <div className="flex items-center justify-between px-4 py-3 bg-gray-100 border-b border-gray-300">
            <div>
              <h3 className="text-lg font-medium text-gray-900">{displayTitle}</h3>
              <p className="text-sm text-gray-600">
                Device: {device?.id || device?.serialNumber || 'Unknown'}
                {remainingTime && <span className="ml-4">Time Left: {remainingTime}</span>}
              </p>
            </div>
            <button
              title="Close Live View"
              className="text-gray-500 hover:text-gray-800 focus:outline-none p-1 rounded-full hover:bg-gray-200"
              onClick={handleClose}
            >
              <span className="sr-only">Close</span>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          
          {/* Video container */}
          <div className="relative bg-black flex-grow flex items-center justify-center" style={{ minHeight: '300px' }}>
            <video
              ref={videoRef}
              id="rtc_liveview" // IMPORTANT: vmRTC requires this specific ID
              className="w-full h-full object-contain"
              autoPlay
              playsInline
              muted={isMuted}
            />
            
            {/* Loading Overlay */}
            {isConnecting && !connectionError && (
              <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-70 text-white">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-white mx-auto mb-3"></div>
                  <p>Connecting to device camera...</p>
                  <div className="mt-3 w-64 bg-gray-700 rounded-full h-2.5">
                    <div className="bg-blue-600 h-2.5 rounded-full" style={{ width: `${connectionProgress}%` }}></div>
                  </div>
                </div>
              </div>
            )}
            
            {/* Error Overlay */}
            {connectionError && (
              <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-80 text-white p-4">
                <div className="text-center">
                  <AlertTriangle size={40} className="text-red-500 mx-auto mb-3" />
                  <h3 className="text-xl font-semibold mb-2">Connection Error</h3>
                  <p className="mb-4 text-gray-300">{connectionError}</p>
                  <button
                    className="mt-2 px-5 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
                    onClick={handleRetry}
                  >
                    Try Again
                  </button>
                  <button
                    className="mt-2 ml-2 px-5 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 focus:outline-none"
                    onClick={handleClose}
                  >
                    Close
                  </button>
                </div>
              </div>
            )}
            
            {/* Live indicator and controls */}
            {!isConnecting && !connectionError && (
              <>
                <div className="absolute top-4 left-4 flex items-center bg-red-600 text-white px-2 py-1 rounded-sm text-xs shadow">
                  <span className="w-2 h-2 bg-white rounded-full mr-1.5 animate-pulse"></span>
                  LIVE
                </div>
                
                <div className="absolute bottom-4 right-4 flex items-center space-x-2">
                  <button
                    title={isMuted ? "Unmute Audio" : "Mute Audio"}
                    onClick={toggleMute}
                    className="bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70 transition-opacity"
                  >
                    {isMuted ? <VolumeX size={20} /> : <Volume2 size={20} />}
                  </button>
                  <button
                    title="Take Snapshot"
                    onClick={handleSnapshot}
                    className="bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70 transition-opacity"
                  >
                    <Camera size={20} />
                  </button>
                  <button
                    title="Switch Camera"
                    onClick={handleSwitchCamera}
                    className="bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70 transition-opacity"
                  >
                    <RefreshCw size={20} />
                  </button>
                </div>
              </>
            )}
          </div>
          
          {/* Footer */}
          <div className="px-4 py-3 bg-gray-100 border-t border-gray-300">
            <div className="flex items-center justify-between">
              <p className="text-xs text-gray-500">
                {remainingTime ? `Session ends automatically.` : `Establishing connection...`}
              </p>
              <button
                className="px-4 py-1.5 bg-gray-500 text-white text-sm rounded hover:bg-gray-600 focus:outline-none"
                onClick={handleClose}
              >
                Close View
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }
  
  // Embedded style (non-modal)
  return (
    <div className="vmx-liveview relative w-full h-full bg-black rounded-lg overflow-hidden">
      <video
        ref={videoRef}
        id="rtc_liveview" // IMPORTANT: vmRTC requires this specific ID
        className="w-full h-full object-contain"
        autoPlay
        playsInline
        muted={isMuted}
      />
      
      {/* Status overlays and controls similar to modal version but more compact */}
      {/* ... */}
    </div>
  );
};

export default VisionMaxLiveView;