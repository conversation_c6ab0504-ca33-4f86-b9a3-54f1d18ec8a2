import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { format, subDays } from 'date-fns';
import { Filter, Download, Plus, Search, Truck, RefreshCw } from 'lucide-react';

import { Card, CardContent, CardHeader, CardTitle } from '../../components/common/Card';
import { Button } from '../../components/common/Button';
import { DataFilter } from '../../components/common/DataFilter';
import { VehiclesTable } from './VehiclesTable';
import { Spinner } from '../../components/common/Spinner';
import { Alert } from '../../components/common/Alert';
import { Pagination } from '../../components/common/Pagination';
import { Vehicle, VisionMaxAPI } from '@/api/visionMaxApi';
import AddVehicleModal from './AddVehicleModal';

// Define types for query parameters
interface VehiclesQueryParams {
  search?: string;
  startDate: string;
  endDate: string;
  page: number;
  perPage: number;
  status?: string;
  type?: string;
  lastActive?: string;
}

// Define filter types
interface FilterOption {
  id: string;
  label: string;
}

interface FilterGroup {
  id: string;
  label: string;
  options: FilterOption[];
}

// Define filter state type
interface FiltersState {
  [key: string]: string[]; // Add index signature to make it compatible with Record<string, string[]>
  status: string[];
  type: string[];
  lastActive: string[];
}

// Define data types based on the usage in the component
interface VehicleStats {
  active: number;
  total: number;
  totalDistance: number;
}

interface VehicleData {
  vehicles: Vehicle[];
  stats?: VehicleStats;
  totalCount?: number;
}

// Enhanced vehicle interface for UI
interface EnhancedVehicle {
  vehicle_id: number;
  license_plate: string;
  vin: string;
  model: string;
  paired_device: {
    id: number;
    name: string;
    serial_number: string;
  } | null;
  status: string; // Changed from number to string for UI
  created_at: number;
  updated_at: number;
  // Additional UI properties
  make?: string;
  type?: string;
  year?: number;
  lastActiveDate?: Date;
  driverName?: string;
  location?: string;
  mileage?: number;
}

const Vehicles: React.FC = () => {
  const navigate = useNavigate();
  // const { api } = useApi();
  
  // Search state
  const [searchTerm, setSearchTerm] = useState<string>('');
  
  // Filter state
  const [dateRange, setDateRange] = useState<{ startDate: Date; endDate: Date }>({
    startDate: subDays(new Date(), 30), // Last 30 days
    endDate: new Date(),
  });
  
  const [filters, setFilters] = useState<FiltersState>({
    status: [],
    type: [],
    lastActive: [],
  });
  
  // Pagination
  const [page, setPage] = useState<number>(1);
  const [perPage, setPerPage] = useState<number>(10);
  
  // Loading and data states
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isFetching, setIsFetching] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);
  const [data, setData] = useState<VehicleData | null>(null);
  const [actionLoading, setActionLoading] = useState<{[key: number]: 'activate' | 'deactivate' | 'delete' | null}>({});

//   // Query parameters based on filters and pagination
//   const queryParams: VehiclesQueryParams = {
//     search: searchTerm || undefined,
//     startDate: format(dateRange.startDate, 'yyyy-MM-dd'),
//     endDate: format(dateRange.endDate, 'yyyy-MM-dd'),
//     page,
//     perPage,
//     ...(filters.status.length > 0 && { status: filters.status.join(',') }),
//     ...(filters.type.length > 0 && { type: filters.type.join(',') }),
//     ...(filters.lastActive.length > 0 && { lastActive: filters.lastActive.join(',') }),
//   };
const token = localStorage.getItem('authToken');

const api = new VisionMaxAPI({
  baseURL: 'https://api.visionmaxfleet.com/', // Use default URL directly
  token
});

const handleVehicleDeactivate = useCallback(async (vehicleId: number) => {
  setActionLoading(prev => ({ ...prev, [vehicleId]: 'deactivate' }));
  
  try {
    const response = await api.deactivateVehicle(vehicleId);
    
    if (response.status === 'success') {
      // Update the vehicle status locally for immediate UI feedback
      setData(prevData => {
        if (!prevData) return prevData;
        
        return {
          ...prevData,
          vehicles: prevData.vehicles.map(vehicle => 
            (vehicle.vehicle_id === vehicleId || vehicle.vehicle_id === vehicleId)
              ? { ...vehicle, status: 0 }
              : vehicle
          ),
          stats: prevData.stats ? {
            ...prevData.stats,
            active: Math.max(0, prevData.stats.active - 1)
          } : undefined
        };
      });
      
      // Optional: Show success notification
      console.log('Vehicle deactivated successfully');
      // You can add toast notification here
    } else {
      throw new Error(response.message || 'Failed to deactivate vehicle');
    }
  } catch (error) {
    console.error('Failed to deactivate vehicle:', error);
    // You can add toast notification here
    setError(error instanceof Error ? error : new Error('Failed to deactivate vehicle'));
  } finally {
    setActionLoading(prev => ({ ...prev, [vehicleId]: null }));
  }
}, [api]);

const handleVehicleActivate = useCallback(async (vehicleId: number) => {
  setActionLoading(prev => ({ ...prev, [vehicleId]: 'activate' }));
  
  try {
    const response = await api.activateVehicle(vehicleId);
    
    if (response.status === 'success') {``
      // Update the vehicle status locally for immediate UI feedback
      setData(prevData => {
        if (!prevData) return prevData;
        
        return {
          ...prevData,
          vehicles: prevData.vehicles.map(vehicle => 
            (vehicle.vehicle_id === vehicleId || vehicle.vehicle_id === vehicleId)
              ? { ...vehicle, status: 1 }
              : vehicle
          ),
          stats: prevData.stats ? {
            ...prevData.stats,
            active: prevData.stats.active + 1
          } : undefined
        };
      });
      
      // Optional: Show success notification
      console.log('Vehicle activated successfully');
      // You can add toast notification here
    } else {
      throw new Error(response.message || 'Failed to activate vehicle');
    }
  } catch (error) {
    console.error('Failed to activate vehicle:', error);
    // You can add toast notification here
    setError(error instanceof Error ? error : new Error('Failed to activate vehicle'));
  } finally {
    setActionLoading(prev => ({ ...prev, [vehicleId]: null }));
  }
}, [api]);

// Handle vehicle deletion
const handleVehicleDelete = useCallback(async (vehicleId: number) => {
  // Show confirmation dialog
  const confirmed = window.confirm('Are you sure you want to delete this vehicle? This action cannot be undone.');
  if (!confirmed) return;

  setActionLoading(prev => ({ ...prev, [vehicleId]: 'delete' }));
  
  try {
    const response = await api.deleteVehicle(vehicleId);
    
    if (response.status === 'success') {
      // Remove the vehicle from the local state for immediate UI feedback
      setData(prevData => {
        if (!prevData) return prevData;
        
        const vehicleToDelete = prevData.vehicles.find(v => 
          v.vehicle_id === vehicleId || v.vehicle_id === vehicleId
        );
        const wasActive = vehicleToDelete?.status === 1;
        
        return {
          ...prevData,
          vehicles: prevData.vehicles.filter(vehicle => 
            vehicle.vehicle_id !== vehicleId && vehicle.vehicle_id !== vehicleId
          ),
          stats: prevData.stats ? {
            ...prevData.stats,
            total: Math.max(0, prevData.stats.total - 1),
            active: wasActive ? Math.max(0, prevData.stats.active - 1) : prevData.stats.active
          } : undefined,
          totalCount: Math.max(0, (prevData.totalCount || 0) - 1)
        };
      });
      
      // Optional: Show success notification
      console.log('Vehicle deleted successfully');
      // You can add toast notification here
    } else {
      throw new Error(response.message || 'Failed to delete vehicle');
    }
  } catch (error) {
    console.error('Failed to delete vehicle:', error);
    // You can add toast notification here
    setError(error instanceof Error ? error : new Error('Failed to delete vehicle'));
  } finally {
    setActionLoading(prev => ({ ...prev, [vehicleId]: null }));
  }
}, [api]);

// Handle row click for vehicle details
const handleRowClick = useCallback((vehicleId: number) => {
  // Navigate to vehicle details page or open modal
  console.log('Navigate to vehicle details:', vehicleId);
  // Example: navigate(`/vehicles/${vehicleId}`);
}, []);
  
const fetchVehicles = async () => {
  try {
    setIsFetching(true);
    if (!isLoading) setIsLoading(true);
    
    // Convert status filter to API expected format
    const statusFilter = filters.status.map(s => s === 'active' ? 1 : 0);
    
    // Call the API with proper pagination and filters
    const response = await api.getVehicleList({
      keyword: searchTerm,
      status: statusFilter.length > 0 ? statusFilter : undefined,
      page: page,
      page_size: perPage
    });
    
    // Check if response or response.data is undefined
    if (!response || !response.data) {
      throw new Error('Invalid API response format');
    }
    
    // Ensure items property exists with fallbacks
    const items = response.data || [];
    
    // Calculate stats from the data
    let activeCount = 0;
    let totalDistance = 0;
    
    const vehicle: Vehicle[] = items.map(vehicle => {
      // Set active count based on vehicle status
      if (vehicle.status === 1) activeCount++;    

      
      return vehicle;
    });
    
    // Create the data structure for the UI
    const vehicleData: VehicleData = {
      vehicles: vehicle,
      stats: {
        active: activeCount,
        total: response.data.length || 0,
        totalDistance: totalDistance
      },
      totalCount: response.data.length || 0
    };
    console.log(vehicleData);
    setData(vehicleData);
    setError(null);
  } catch (err) {
    console.error('Error fetching vehicles:', err);
    // Ensure a fallback data structure to prevent rendering errors
    setData({
      vehicles: [],
      stats: { active: 0, total: 0, totalDistance: 0 },
      totalCount: 0
    });
    setError(err instanceof Error ? err : new Error('Failed to load vehicles'));
  } finally {
    setIsLoading(false);
    setIsFetching(false);
  }
};

  
  // Reset to page 1 when filters or search changes
  useEffect(() => {
    setPage(1);
  }, [dateRange, filters, searchTerm]);
  
  // Fetch data when parameters change
  useEffect(() => {
    fetchVehicles();
  }, [page, perPage, searchTerm, filters, dateRange]);
  
  // Handle date range change
  const handleDateRangeChange = (startDate: Date, endDate: Date) => {
    setDateRange({ startDate, endDate });
  };
  
  // Handle filter change
  const handleFilterChange = (filterId: string, values: string[]) => {
    setFilters(prev => ({
      ...prev,
      [filterId]: values,
    }));
  };
  
  // Handle clear filters
  const handleClearFilters = () => {
    setFilters({
      status: [],
      type: [],
      lastActive: [],
    });
    setSearchTerm('');
  };
  
  // Handle search
  const handleSearch = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);
    const search = formData.get('search') as string;
    setSearchTerm(search);
  };
  const [isAddVehicleModalOpen, setIsAddVehicleModalOpen] = useState(false);

  const handleVehicleAdded = () => {
    // Refresh the vehicles list
    fetchVehicles();
  };
  
  // Handle add vehicle
  const handleAddVehicle = () => {
    setIsAddVehicleModalOpen(true);
  };
  
  // Handle modal close
  const handleCloseModal = () => {
    setIsAddVehicleModalOpen(false);
  };
  
  // Handle refresh
  const handleRefresh = () => {
    fetchVehicles();
  };
  
  // Filter groups
  const filterGroups: FilterGroup[] = [
    {
      id: 'status',
      label: 'Status',
      options: [
        { id: 'active', label: 'Active' },
        { id: 'inactive', label: 'Inactive' },
        { id: 'maintenance', label: 'Maintenance' },
      ],
    },
    {
      id: 'type',
      label: 'Vehicle Type',
      options: [
        { id: 'truck', label: 'Truck' },
        { id: 'van', label: 'Van' },
        { id: 'car', label: 'Car' },
        { id: 'trailer', label: 'Trailer' },
        { id: 'other', label: 'Other' },
      ],
    },
    {
      id: 'lastActive',
      label: 'Last Active',
      options: [
        { id: 'today', label: 'Today' },
        { id: 'yesterday', label: 'Yesterday' },
        { id: 'lastWeek', label: 'Last Week' },
        { id: 'lastMonth', label: 'Last Month' },
      ],
    },
  ];
  
  // Type guard for vehicle data
  const isVehicleData = (data: any): data is VehicleData => {
    return data && typeof data === 'object' && Array.isArray(data.vehicles);
  };
  
  return (
    <div className="space-y-6">
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
        <h1 className="text-2xl font-bold">Vehicles</h1>
        
        <div className="flex space-x-3">
          <Button 
            variant="outline" 
            leftIcon={<RefreshCw size={16} />}
            onClick={handleRefresh}
            isLoading={isFetching}
          >
            Refresh
          </Button>
          <Button 
            variant="outline" 
            leftIcon={<Download size={16} />}
          >
            Export
          </Button>
          <Button 
            onClick={handleAddVehicle} 
            leftIcon={<Plus size={16} />}
          >
            Add Vehicle
          </Button>
        </div>
      </div>

      <AddVehicleModal
        isOpen={isAddVehicleModalOpen}
        onClose={handleCloseModal}
        onSuccess={handleVehicleAdded}
      />
      
      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Filter size={18} className="mr-2" />
            Search and Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Search form */}
            <form onSubmit={handleSearch} className="flex space-x-2">
              <div className="relative flex-1">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Search size={16} className="text-gray-400" />
                </div>
                <input
                  type="text"
                  name="search"
                  placeholder="Search by vehicle ID, make, model, or license plate..."
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md"
                  defaultValue={searchTerm}
                />
              </div>
              <Button type="submit">Search</Button>
            </form>
            
            {/* Filters */}
            <DataFilter
              dateRange={dateRange}
              filters={filterGroups}
              activeFilters={filters}
              onDateRangeChange={handleDateRangeChange}
              onFilterChange={handleFilterChange}
              onClearFilters={handleClearFilters}
            />
          </div>
        </CardContent>
      </Card>
      
      {/* Status Overview */}
      {!isLoading && !error && isVehicleData(data) && data.stats && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card className="bg-green-50">
            <CardContent className="p-4">
              <div className="flex items-center">
                <div className="p-2 bg-green-100 rounded-lg">
                  <Truck size={24} className="text-green-600" />
                </div>
                <div className="ml-4">
                  <div className="text-sm text-green-700 font-medium">Active Vehicles</div>
                  <div className="text-2xl font-bold">{data.stats.active}</div>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card className="bg-blue-50">
            <CardContent className="p-4">
              <div className="flex items-center">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Truck size={24} className="text-blue-600" />
                </div>
                <div className="ml-4">
                  <div className="text-sm text-blue-700 font-medium">Total Distance</div>
                  <div className="text-2xl font-bold">{data.stats.totalDistance.toLocaleString()} km</div>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card className="bg-purple-50">
            <CardContent className="p-4">
              <div className="flex items-center">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <Truck size={24} className="text-purple-600" />
                </div>
                <div className="ml-4">
                  <div className="text-sm text-purple-700 font-medium">Total Vehicles</div>
                  <div className="text-2xl font-bold">{data.stats.total}</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
      
      {/* Vehicles Table */}
      <Card>
        <CardHeader>
          <CardTitle>Vehicle List</CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          {isLoading ? (
            <div className="flex justify-center items-center p-8">
              <Spinner size="large" color="primary" />
            </div>
          ) : error ? (
            <Alert
              type="error"
              title="Error Loading Vehicles"
              message={error.message || "There was a problem loading the vehicles data. Please try again."}
              action={{
                label: 'Retry',
                onClick: handleRefresh
              }}
              className="m-4"
            />
          ) : isVehicleData(data) && data.vehicles.length === 0 ? (
            <div className="text-center p-8 text-gray-500">
              No vehicles found matching your filters.
            </div>
          ) : (
            <VehiclesTable
              vehicles={isVehicleData(data) ? data.vehicles : []}
              isLoading={isFetching}
              onRowClick={(vehicleId: any) => navigate(`/vehicles/${vehicleId}`)}
              onVehicleActivate={handleVehicleActivate}   
              onVehicleDeactivate={handleVehicleDeactivate}
              onVehicleDelete={handleVehicleDelete} 
            />
          )}
        </CardContent>
      </Card>
      
      {/* Pagination */}
      {!isLoading && !error && isVehicleData(data) && data.totalCount > 0 && (
        <div className="flex justify-between items-center">
          <div className="text-sm text-gray-500">
            Showing {data.vehicles.length} of {data.totalCount} vehicles
          </div>
          <div className="flex items-center space-x-4">
            <select
              className="px-2 py-1 border rounded-md text-sm"
              value={perPage}
              onChange={(e) => setPerPage(Number(e.target.value))}
            >
              <option value={10}>10 per page</option>
              <option value={25}>25 per page</option>
              <option value={50}>50 per page</option>
              <option value={100}>100 per page</option>
            </select>
            
            <Pagination
              currentPage={page}
              totalPages={Math.ceil(data.totalCount / perPage)}
              onPageChange={setPage}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default Vehicles;